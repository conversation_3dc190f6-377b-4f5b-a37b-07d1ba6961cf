[{"replyText": "好的，已为您保留当前保单，后续如有调整需求可随时联系我，感谢您的信任😊", "stepDesc": "用户仅有赠险-用户确认", "stepCode": "C18", "intentionCode": "surrender_insurance", "nodeDesc": "仅有赠险：赠险无需费用告知&索要扣费截图（未提供，要求退保赠险/不相信免费）-无费用+无需退保告知/无需费用二次告知（认可）-赠险保留结束语下发", "_score": 0.028170876, "编号": "63990d68-2a2a-4570-8cf6-f013a908a79a", "intentionName": "要求退保"}, {"replyText": "亲☺️，非常抱歉，给您带来不好的体验，请问您是什么原因退保呢？🤔", "stepDesc": "多张保单-询问退保原因", "stepCode": "A02", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-目标挽单列表多张保单：询问退保原因", "_score": 0.028170876, "编号": "9ed7a5ca-4d16-4d0b-babe-7f95fca54ef2", "intentionName": "要求退保"}, {"replyText": "辛苦您回忆下当时投保的手机号，我为您查询下，当前手机尾号${手机尾号!}下没有查询到呢。", "stepDesc": "用户仅有赠险-手机号确认", "stepCode": "C22", "intentionCode": "surrender_insurance", "nodeDesc": "仅有赠险：赠险无需费用告知&索要扣费截图（全部我司扣费截图）-其他手机号确认", "_score": 0.028170876, "编号": "ba7705c2-83e4-4fd5-a4eb-1e75559b1c83", "intentionName": "要求退保"}, {"replyText": "好的${投保人姓名!}<#if user_gender??><#if user_gender=='1'>先生<#else>女士</#if><#else></#if>，那如果后续有任何问题您可以随时联系我，感谢您对众安经纪的支持🌹", "stepDesc": "无保单-全部非我司-无保单确认服务结束", "stepCode": "C06", "intentionCode": "surrender_insurance", "nodeDesc": "用户名下无单：无保单告知&索要扣费截图（全部非我司扣费截图）-司外保单告知（认可）-结束语下发", "_score": 0.028170876, "编号": "ff23d737-5678-48e1-ab31-5013da786d84", "intentionName": "要求退保"}, {"replyText": "好的${投保人姓名!}<#if user_gender??><#if user_gender=='1'>先生<#else>女士</#if><#else></#if>，那如果后续有任何问题您可以随时联系我，感谢您对众安经纪的支持🌹", "stepDesc": "用户在解释后认可/同意，结束服务", "stepCode": "C21", "intentionCode": "surrender_insurance", "nodeDesc": "仅有赠险：赠险无需费用告知&索要扣费截图（未提供，要求退保赠险/不相信免费）-无费用+无需退保告知/无需费用二次告知（仍要退保）-赠险退保解释一次（仍要退保）-赠险退保解释二次（认可）-赠险保留结束语下发", "_score": 0.028170876, "编号": "e2b79262-834b-4187-9678-397cf6134040", "intentionName": "要求退保"}, {"replyText": "好的${投保人姓名!}<#if user_gender??><#if user_gender=='1'>先生<#else>女士</#if><#else></#if>，那如果后续有任何问题您可以随时联系我，感谢您对众安经纪的支持🌹", "stepDesc": "用户在解释后认可/同意，结束服务", "stepCode": "C20", "intentionCode": "surrender_insurance", "nodeDesc": "仅有赠险：赠险无需费用告知&索要扣费截图（未提供，要求退保赠险/不相信免费）-无费用+无需退保告知/无需费用二次告知（仍要退保）-赠险退保解释一次（认可）-赠险保留结束语下发", "_score": 0.028170876, "编号": "c1517922-ab39-4098-9726-c80d8abc9b71", "intentionName": "要求退保"}, {"replyText": "辛苦您提供一下投保手机号哈，我立刻帮您查询～", "stepDesc": "询问用户手机号", "stepCode": "A01", "intentionCode": "surrender_insurance", "nodeDesc": "引导登录：引导核身（未操作）-手机号核身", "_score": 0.028170876, "编号": "ae5bdc05-3e8f-4a6a-9ce0-e2f9fd86de97", "intentionName": "要求退保"}, {"replyText": "亲😟，我懂您嫌重疾险保费高。但您想，一旦患重病，治疗费不堪设想。咱这重疾险，确诊合同约定重疾，立马一次性赔笔大钱💸。这钱咋用您定，治病、补收入损失、请护工都行。就像给未来加把牢固保险锁🔒，为您和家人兜底，很值的。要不别轻易退保，再考虑下🥰？", "stepDesc": "产品讲解（责任不清楚）--重疾险", "stepCode": "Z16", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-目标挽单列表仅一张保单：险类：重疾-无损第一轮", "_score": 0.028170876, "编号": "f719871c-3d1b-43d9-a479-8547364e3695", "intentionName": "要求退保"}, {"replyText": "亲😃，您这份房屋保险超棒的👍！它能抵御常见场景风险，要是真遇到啥事儿，在责任范围内的损失都能得到补偿呢。真心建议您保留下来，给房子多份安心保障🏠~\n", "stepDesc": "产品讲解（责任不清楚）--家财险", "stepCode": "Z17", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-目标挽单列表仅一张保单：险类：家财-无损第一轮", "_score": 0.028170876, "编号": "1980b072-c589-4016-8673-08f53a1f5484", "intentionName": "要求退保"}, {"replyText": "亲😃，我又仔细帮您核实啦，用您进线的这个号码查不到有效保单。要是您没有其他手机号的话，麻烦您查看下扣费信息，仔细核对一下扣费方呢🤗。", "stepDesc": "进入挽单流程-定位保单-无历史记录：索要扣费截图（未提供：未提及全退）-确认保单截图（提供：识别保单均为赠险）-赠险无需费用告知&索要其他扣费截图", "stepCode": "Z03", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-定位保单-无历史记录：索要扣费截图（未提供：未提及全退）-确认保单截图（提供：识别保单均为赠险）-赠险无需费用告知&索要其他扣费截图", "_score": 0.028170876, "编号": "480cd8f8-db33-4e65-8642-56f4d9821c40", "intentionName": "要求退保"}, {"replyText": "因为您已投保，是众安优保的尊享会员，看您还没有领取【免费体检】和【门急诊赠险】等会员权益，我这边发送给您看一下，性价比还蛮高的，如果您觉得不合适可以再申请退保，您看可以吗？", "stepDesc": "权益挽单--单张", "stepCode": "Z02", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-目标挽单列表仅一张保单：险类：${险类}-无损第一轮（未同意）-无损第二轮（未同意）-退保损失讲解（未同意）-权益挽单", "_score": 0.028170876, "编号": "a7aa9d4d-4c11-46c5-903c-e1588e5c8fa6", "extraInfo": "[{\"newsDescription\":\"点击领取\",\"newsTitle\":\"门急诊赠险权益已送达\",\"newsUrl\":\"https://nmpcdn.zhonganib.com/nmp/1748420220262/20fa0b953b9c11f09d5096b2bc6a2915.png?&w=200&h=200\",\"type\":\"news\"},{\"newsDescription\":\"点击领取\",\"newsTitle\":\"免费体检权益已送达\",\"newsUrl\":\"https://nmpcdn.zhonganib.com/nmp/1748420220262/20fa0b953b9c11f09d5096b2bc6a2915.png?&w=200&h=200\",\"type\":\"news\"}]", "intentionName": "要求退保"}, {"replyText": "您是否还记得大概扣了多少费用，或者您购买的产品名称呢？", "stepDesc": "进入挽单流程-定位保单-无历史记录：索要扣费截图（未提供：未提及全退）-确认保单截图（未提供：未提及全退）-确认扣费金额/保单名称", "stepCode": "C01", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-定位保单-无历史记录：索要扣费截图（未提供：未提及全退）-确认保单截图（未提供：未提及全退）-确认扣费金额/保单名称", "_score": 0.028170876, "编号": "ff8e345f-198f-46d4-b4e7-3b933b2527cd", "intentionName": "要求退保"}, {"replyText": "您申请退保的保单信息如下：\n1.产品名称：${产品名称!}\n✅被保人：${被保人姓名!}\n✅交费金额：${月保费!}元/月（年交：${年保费!}元/年）\n✅预计退费金额：${退保金额!}\n您看是这份吗", "stepDesc": "退保确认--单张", "stepCode": "C15", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-目标挽单列表仅一张保单：险类：${险类}-无损第一轮（未同意）-无损第二轮（未同意）-退保损失讲解（未同意）-权益挽单（未同意）-解停挽单（未同意）-挽单失败确认保单", "_score": 0.028170876, "编号": "7281d0b9-a452-49d7-a3f4-844145e0e7bf", "intentionName": "要求退保"}, {"replyText": "实在抱歉给您带来不便啦。但退保得投保人本人确认，通过自助链接不仅方便，还能更好保护您个人信息哦。我已经帮您申请了极速退保通道权限，只要三步就能退保成功～您看看这操作图👇", "stepDesc": "引导用户自行退保", "stepCode": "G05", "intentionCode": "surrender_insurance", "nodeDesc": "退保-目标退保多单：引导用户确认退保金额（认可）-发送退保工具（未操作）-引导自行操作", "_score": 0.028170876, "编号": "133446b6-1928-42b2-8b48-36a1cd910b6f", "extraInfo": "[{\"imageUrl\":\"https://nmpcdn.zhonganib.com/nmp/1747652416863/727c0f0c34a011f0a5c6b60542ca4545.png?&w=750&h=2815\",\"type\":\"image\"},{\"content\":\"按步骤来很简单哒，要是有啥不懂随时问我🤗。\",\"type\":\"text\"}]", "intentionName": "要求退保"}, {"replyText": "实在抱歉给您带来不便啦。但退保得投保人本人确认，通过自助链接不仅方便，还能更好保护您个人信息哦。我已经帮您申请了极速退保通道权限，只要三步就能退保成功～您看看这操作图👇", "stepDesc": "引导用户自行退保", "stepCode": "G04", "intentionCode": "surrender_insurance", "nodeDesc": "退保-目标退保一单：引导用户确认退保金额（认可）-发送退保工具（未操作）-引导自行操作", "_score": 0.028170876, "编号": "c15789f6-9ff9-4615-9e4c-433073a9fa23", "extraInfo": "[{\"imageUrl\":\"https://nmpcdn.zhonganib.com/nmp/1747652416863/727c0f0c34a011f0a5c6b60542ca4545.png?&w=750&h=2815\",\"type\":\"image\"},{\"content\":\"按步骤来很简单哒，要是有啥不懂随时问我🤗。\",\"type\":\"text\"}]", "intentionName": "要求退保"}, {"replyText": "实在抱歉给您带来不便啦。但退保得投保人本人确认，通过自助链接不仅方便，还能更好保护您个人信息哦。我已经帮您申请了极速退保通道权限，只要三步就能退保成功～您看看这操作图👇", "stepDesc": "引导用户自行退保", "stepCode": "G03", "intentionCode": "surrender_insurance", "nodeDesc": "退保-目标退保多单：引导用户确认退保金额（不认可）-解释有损退（认可）-发送退保工具（未操作）-引导自行操作", "_score": 0.028170876, "编号": "a342f3a2-d311-420f-8799-949d7bfabed6", "extraInfo": "[{\"imageUrl\":\"https://nmpcdn.zhonganib.com/nmp/1747652416863/727c0f0c34a011f0a5c6b60542ca4545.png?&w=750&h=2815\",\"type\":\"image\"},{\"content\":\"按步骤来很简单哒，要是有啥不懂随时问我🤗。\",\"type\":\"text\"}]", "intentionName": "要求退保"}, {"replyText": "辛苦您回想一下扣费金额或者保单名称，然后发给我", "stepDesc": "发送确认扣费金额/保单名称话术", "stepCode": "A04", "intentionCode": "surrender_insurance", "nodeDesc": "定位保单-用户名下<#if insurance_size gt 1>多单<#else>一单</#if>-不满足挽单条件：索要扣费截图（未提供）-确认扣费金额/保单名称", "_score": 0.028170876, "编号": "415ed04f-acc6-4627-8dd1-3cf200f56f75", "intentionName": "要求退保"}, {"replyText": "需人工处理：用户发送扣费截图，识别失败", "stepDesc": "转人工", "stepCode": "T13", "intentionCode": "surrender_insurance", "nodeDesc": "仅有赠险：赠险无需费用告知&索要扣费截图（扣费截图识别失败）-转人工，扣费截图识别失败", "_score": 0.028170876, "编号": "6818076c-c60e-4377-a13e-c8a424b76d72", "intentionName": "要求退保"}, {"replyText": "需人工处理：存在我司+非我司扣费截图", "stepDesc": "转人工", "stepCode": "T12", "intentionCode": "surrender_insurance", "nodeDesc": "仅有赠险：赠险无需费用告知&索要扣费截图（存在我司+非我司扣费截图）-转人工，复杂场景", "_score": 0.028170876, "编号": "29e5a13f-4bf3-4040-ae17-1c09235d33e6", "intentionName": "要求退保"}, {"replyText": "需人工处理：用户否认保单", "stepDesc": "用户否认保单", "stepCode": "T08", "intentionCode": "surrender_insurance", "nodeDesc": "确认保单-用户名下<#if insurance_size gt 1>多单<#else>一单</#if>-不满足挽单条件：下发确认保单（否认）-走过定位保单流程-转人工，用户否认保单", "_score": 0.028170876, "编号": "8b156941-d5e7-4e01-83d7-61824a52823a", "intentionName": "要求退保"}, {"replyText": "需人工处理：存在我司+非我司扣费截图", "stepDesc": "转人工", "stepCode": "T37", "intentionCode": "surrender_insurance", "nodeDesc": "用户名下无单：无保单告知&索要扣费截图（存在我司+非我司扣费截图）-转人工，复杂场景", "_score": 0.028170876, "编号": "c5040fe6-e12b-4a23-9353-0a7aef7d73e7", "intentionName": "要求退保"}, {"replyText": "需人工处理：用户咨询其他问题", "stepDesc": "转人工", "stepCode": "T14", "intentionCode": "surrender_insurance", "nodeDesc": "仅有赠险：赠险无需费用告知&索要扣费截图（未提供，表述其他）-转人工，用户咨询其他", "_score": 0.028170876, "编号": "832d3387-2809-433b-98e4-370a56e56982", "intentionName": "要求退保"}, {"replyText": "需人工处理：用户发送扣费截图，识别失败", "stepDesc": "转人工", "stepCode": "T38", "intentionCode": "surrender_insurance", "nodeDesc": "用户名下无单：无保单告知&索要扣费截图（扣费截图识别失败）-转人工，扣费截图识别失败", "_score": 0.028170876, "编号": "ed157099-4610-4de8-8103-d33d04d22f9c", "intentionName": "要求退保"}, {"replyText": "需人工处理：存在ocr识别失败", "stepDesc": "转人工", "stepCode": "T60", "intentionCode": "surrender_insurance", "nodeDesc": "定位保单-用户名下<#if insurance_size gt 1>多单<#else>一单</#if>-不满足挽单条件：索要扣费截图（未提供）-确认扣费金额/保单名称（未提供）-查保单工具/截图（提供）-转人工，存在ocr识别失败", "_score": 0.028170876, "编号": "5ee44e3e-c99b-4a88-b856-91cb1c5f7380", "intentionName": "要求退保"}, {"replyText": "需人工处理：用户未提供保单信息，或信息无法定位保单", "stepDesc": "用户未提供保单信息，或信息无法定位保单", "stepCode": "T30", "intentionCode": "surrender_insurance", "nodeDesc": "定位保单-用户名下<#if insurance_size gt 1>多单<#else>一单</#if>-不满足挽单条件：索要扣费截图（未提供）-确认扣费金额/保单名称（未提供）-查保单工具/截图（未提供）-转人工，用户未提供任何信息", "_score": 0.028170876, "编号": "d38288bb-522a-4c8f-adca-d2203548f30f", "intentionName": "要求退保"}, {"replyText": "需人工处理：扣费截图与手机号查询保单未匹配", "stepDesc": "转人工", "stepCode": "T41", "intentionCode": "surrender_insurance", "nodeDesc": "用户名下无单：无保单告知&索要扣费截图（全部我司扣费截图）-其他手机号确认（提供）-扣费截图与手机号查询保单未匹配，转人工", "_score": 0.028170876, "编号": "46feb40f-707f-4c88-b8e1-61e9eede5db3", "intentionName": "要求退保"}, {"replyText": "需人工处理：用户不认可有损退解释", "stepDesc": "转人工", "stepCode": "T42", "intentionCode": "surrender_insurance", "nodeDesc": "退保-目标退保一单：引导用户确认退保金额（不认可）-解释有损退（不认可）-转人工，用户不认可有损退", "_score": 0.028170876, "编号": "9a9d3a30-947b-4ae7-99c6-663104e40dda", "intentionName": "要求退保"}, {"replyText": "需人工处理：用户咨询其他", "stepDesc": "转人工", "stepCode": "T17", "intentionCode": "surrender_insurance", "nodeDesc": "仅有赠险：赠险无需费用告知&索要扣费截图（未提供，要求退保赠险/不相信免费）-无费用+无需退保告知/无需费用二次告知（仍要退保）-赠险退保解释一次（仍要退保）-赠险退保解释二次（其他）-转人工，咨询其他", "_score": 0.028170876, "编号": "290f10e5-e51f-4577-a4b0-24b28d7a7e5d", "intentionName": "要求退保"}, {"replyText": "需人工处理：引导自行操作后，用户未操作，再次进线", "stepDesc": "转人工", "stepCode": "T50", "intentionCode": "surrender_insurance", "nodeDesc": "退保-目标退保一单：引导用户确认退保金额（认可）-发送退保工具（未操作）-引导自行操作（未操作）-转人工，引导自行操作后用户未操作", "_score": 0.028170876, "编号": "f90ca5e4-883d-490d-ab37-3746268cf893", "intentionName": "要求退保"}, {"replyText": "需人工处理：用户执行完退保操作", "stepDesc": "转人工", "stepCode": "T54", "intentionCode": "surrender_insurance", "nodeDesc": "退保-目标退保一单：引导用户确认退保金额（认可）-发送退保工具（操作）-转人工，用户操作退保后再次进线", "_score": 0.028170876, "编号": "e2371eed-8ab4-4518-ae69-e7fcceeff327", "intentionName": "要求退保"}, {"replyText": "需人工处理：用户执行完退保操作", "stepDesc": "转人工", "stepCode": "T55", "intentionCode": "surrender_insurance", "nodeDesc": "退保-目标退保多单：引导用户确认退保金额（认可）-发送退保工具（操作）-转人工，用户操作退保后再次进线", "_score": 0.028170876, "编号": "178b39aa-2927-4c4d-b223-99ae9a1449ac", "intentionName": "要求退保"}, {"replyText": "需人工处理：存在ocr识别失败", "stepDesc": "转人工", "stepCode": "T81", "intentionCode": "surrender_insurance", "nodeDesc": "引导登录：引导核身（未操作）-手机号核身（未提供）-查保单工具/截图（提供）-转人工，存在ocr识别失败", "_score": 0.028170876, "编号": "381cc9a8-fff8-4953-9d9d-9df00cd9dabf", "intentionName": "要求退保"}, {"replyText": "需人工处理：引导自行操作后，用户未操作，再次进线", "stepDesc": "转人工", "stepCode": "T45", "intentionCode": "surrender_insurance", "nodeDesc": "退保-目标退保多单：引导用户确认退保金额（不认可）-解释有损退（认可）-发送退保工具（未操作）-引导自行操作（未操作）-转人工，引导自行操作后用户未操作", "_score": 0.028170876, "编号": "d6ffdbc2-97e1-49d9-afe9-01b13bd3cd4d", "intentionName": "要求退保"}, {"replyText": "需人工处理：多轮解释仍退保赠险", "stepDesc": "转人工", "stepCode": "T18", "intentionCode": "surrender_insurance", "nodeDesc": "仅有赠险：赠险无需费用告知&索要扣费截图（未提供，要求退保赠险/不相信免费）-无费用+无需退保告知/无需费用二次告知（仍要退保）-赠险退保解释一次（仍要退保）-赠险退保解释二次（仍要退保）-转人工，多轮解释仍退保赠险", "_score": 0.028170876, "编号": "ec4d6346-a878-4c86-bdf8-0d1cd7783f97", "intentionName": "要求退保"}, {"replyText": "需人工处理：二轮挽单后用户同意部分保留", "stepDesc": "转人工", "stepCode": "T75", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-目标挽单列表多张保单：无损第一轮（全部不同意）-无损第二轮（部分同意）-转人工，复杂场景：无损二挽，用户同意保留部分保单，需人工区分", "_score": 0.028170876, "编号": "2adc8731-febd-4a1c-a922-3140885363c2", "intentionName": "要求退保"}, {"replyText": "需人工处理：引导自行操作后，用户操作完退保之后再次进线", "stepDesc": "转人工", "stepCode": "T47", "intentionCode": "surrender_insurance", "nodeDesc": "退保-目标退保多单：引导用户确认退保金额（不认可）-解释有损退（认可）-发送退保工具（未操作）-引导自行操作（操作）-转人工，用户操作退保后再次进线", "_score": 0.028170876, "编号": "24a29458-3791-42e6-9643-0bb3db0e8d2f", "intentionName": "要求退保"}, {"replyText": "需人工处理：引导自行操作后，用户操作完退保之后再次进线", "stepDesc": "转人工", "stepCode": "T52", "intentionCode": "surrender_insurance", "nodeDesc": "退保-目标退保一单：引导用户确认退保金额（认可）-发送退保工具（未操作）-引导自行操作（操作）-转人工，用户操作退保后再次进线", "_score": 0.028170876, "编号": "ca6006bc-5fa7-410a-b798-77f5d32f11cf", "intentionName": "要求退保"}, {"replyText": "需人工处理：定位到保单后，存在与用户名下不匹配得保单", "stepDesc": "转人工-定位到保单后，存在与用户名下不匹配得保单", "stepCode": "T29", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-定位保单-T-1有历史记录：T-1内有扣费截图-转人工，存在非我司扣费截图", "_score": 0.028170876, "编号": "6e5158e5-17d0-45f6-87c7-44426ae541d1", "intentionName": "要求退保"}, {"replyText": "需人工处理：单张保单-发送退保链接后，用户再次进线，已操作退保", "stepDesc": "单张保单-发送退保链接后，用户再次进线，已操作退保", "stepCode": "T25", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-目标挽单列表仅一张保单：险类：${险类}-无损第一轮（未同意）-无损第二轮（未同意）-退保损失讲解（未同意）-权益挽单（未同意）-解停挽单（未同意）-挽单失败确认保单（未否认）-发送退保链接-用户再次进线：操作退保，转人工", "_score": 0.028170876, "编号": "13301a32-5b12-4b61-8c58-48db2e8f2819", "intentionName": "要求退保"}, {"replyText": "需人工处理：多单-发送退保链接后用户已经退保再次进线", "stepDesc": "转人工-多单-发送退保链接后用户已经退保再次进线", "stepCode": "T24", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-目标挽单列表多张保单：无损第一轮（全部不同意）-无损第二轮（全部不同意）-退保损失讲解（全部不同意）-权益挽单（全部不同意）-解停挽单（全部不同意）-挽单失败确认保单（未否认）-发送退保链接-用户再次进线：操作退保，转人工", "_score": 0.028170876, "编号": "98056a90-6d04-40e8-9848-480afd41c81b", "intentionName": "要求退保"}, {"replyText": "需人工处理：没有价值险", "stepDesc": "转人工", "stepCode": "T35", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-定位保单-有历史记录：T-1内无扣费截图-T-1内无保单截图-T-1未提供扣费金额/保单名称-T-1用户提及全退（侧边栏无符合挽单保单）-转人工，存在未匹配到退保保单", "_score": 0.028170876, "编号": "ed204179-4700-4e5a-95b8-8870b14299c2", "intentionName": "要求退保"}, {"replyText": "需人工处理：多单-发送退保链接后用户再次进线-位操作退保", "stepDesc": "转人工-在发送退保链接之后用户再次进线-未操作退保", "stepCode": "T56", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-目标挽单列表多张保单：无损第一轮（全部不同意）-无损第二轮（全部不同意）-退保损失讲解（全部不同意）-权益挽单（全部不同意）-解停挽单（全部不同意）-挽单失败确认保单（未否认）-发送退保链接-用户再次进线：未操作退保，转人工", "_score": 0.028170876, "编号": "a9f3a697-4fb7-4456-8138-d0c2e4ceb380", "intentionName": "要求退保"}, {"replyText": "需人工处理：存在ocr识别与名下保单不匹配", "stepDesc": "进入挽单流程-定位保单-无历史记录：索要扣费截图（未提供：未提及全退）-确认保单截图（提供）-转人工，存在ocr识别与名下保单不匹配", "stepCode": "T59", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-定位保单-无历史记录：索要扣费截图（未提供：未提及全退）-确认保单截图（提供）-转人工，存在ocr识别与名下保单不匹配", "_score": 0.028170876, "编号": "8e64f8ad-4452-4ba0-80ce-c2963eeabc7c", "intentionName": "要求退保"}, {"replyText": "需人工处理：存在未匹配到退保保单", "stepDesc": "进入挽单流程-定位保单-无历史记录：索要扣费截图（未提供：未提及全退）-确认保单截图（未提供：未提及全退）-确认扣费金额/保单名称（未提供：侧边栏无符合挽单保单）-转人工，存在未匹配到退保保单", "stepCode": "T28", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-定位保单-无历史记录：索要扣费截图（未提供：未提及全退）-确认保单截图（未提供：未提及全退）-确认扣费金额/保单名称（未提供：侧边栏无符合挽单保单）-转人工，存在未匹配到退保保单", "_score": 0.028170876, "编号": "ea19efce-4a30-4838-99c8-569b1209b05d", "intentionName": "要求退保"}, {"replyText": "实在抱歉给您带来不便啦。但退保得投保人本人确认，通过自助链接不仅方便，还能更好保护您个人信息哦。我已经帮您申请了极速退保通道权限，只要三步就能退保成功～您看看这操作图👇", "stepDesc": "解释有损退后，引导用户自行退保", "stepCode": "G02", "intentionCode": "surrender_insurance", "nodeDesc": "退保-目标退保一单：引导用户确认退保金额（不认可）-解释有损退（认可）-发送退保工具（未操作）-引导自行操作", "_score": 0.028170876, "编号": "b48529f5-0f91-47bc-9fd7-01a57fa3df7b", "extraInfo": "[{\"imageUrl\":\"https://nmpcdn.zhonganib.com/nmp/1747652416863/727c0f0c34a011f0a5c6b60542ca4545.png?&w=750&h=2815\",\"type\":\"image\"},{\"content\":\"按步骤来很简单哒，要是有啥不懂随时问我🤗。\",\"type\":\"text\"}]", "intentionName": "要求退保"}, {"replyText": "需人工处理：用户提供手机号但手机号查询保单未匹配", "stepDesc": "转人工", "stepCode": "T32", "intentionCode": "surrender_insurance", "nodeDesc": "用户名下无单：无保单告知&索要扣费截图（未提供，要求退保）-无单二次解释并引导登录（未登录）-其他手机号确认（提供）-未定位到保单，转人工", "_score": 0.028170876, "编号": "8257c088-32e9-4977-ae25-6427c8b8d0cb", "intentionName": "要求退保"}, {"replyText": "请点击上方发送给您的“极速退保”链接。操作的时候，一定要准确选好要退保的保单，认真确认退费金额，千万别误操作！要是有任何疑问，随时联系我们就行。", "stepDesc": "发送极速退保链接+操作流程-多单(认可有损退)", "stepCode": "S02", "intentionCode": "surrender_insurance", "nodeDesc": "退保-目标退保多单：引导用户确认退保金额（不认可）-解释有损退（认可）-发送退保工具", "_score": 0.028170876, "编号": "9ad61122-7fdf-42ee-bcf9-add3e619dbc4", "extraInfo": "[{\"miniChannel\":\"JJ_MINIAPP_ZAB\",\"miniContent\":\"极速退保通道\",\"miniImageUrl\":\"https://nmpcdn.zhonganib.com/nmp/1729840628541/2553bf1292a111efb7406ae51353cbbb.png?&w=700&h=592\",\"miniPath\":\"/pages/finance/commonTransfer/index?pageType=dischargeList&operateCode=${surrenderCode}\",\"miniTitle\":\"极速退保通道\",\"type\":\"miniprogrampage\"},{\"imageUrl\":\"https://nmpcdn.zhonganib.com/nmp/1747652416863/727c0f0c34a011f0a5c6b60542ca4545.png?&w=750&h=2815\",\"type\":\"image\"}]", "intentionName": "要求退保"}, {"replyText": "请点击上方发送给您的“极速退保”链接。操作的时候，一定要准确选好要退保的保单，认真确认退费金额，千万别误操作！要是有任何疑问，随时联系我们就行。", "stepDesc": "发送极速退保链接+操作流程(认可退费)", "stepCode": "S03", "intentionCode": "surrender_insurance", "nodeDesc": "退保-目标退保一单：引导用户确认退保金额（认可）-发送退保工具", "_score": 0.028170876, "编号": "44764f9e-e701-494a-b3b6-d4072deb5226", "extraInfo": "[{\"miniChannel\":\"JJ_MINIAPP_ZAB\",\"miniContent\":\"极速退保通道\",\"miniImageUrl\":\"https://nmpcdn.zhonganib.com/nmp/1729840628541/2553bf1292a111efb7406ae51353cbbb.png?&w=700&h=592\",\"miniPath\":\"/pages/finance/commonTransfer/index?pageType=dischargeList&operateCode=${surrenderCode}\",\"miniTitle\":\"极速退保通道\",\"type\":\"miniprogrampage\"},{\"imageUrl\":\"https://nmpcdn.zhonganib.com/nmp/1747652416863/727c0f0c34a011f0a5c6b60542ca4545.png?&w=750&h=2815\",\"type\":\"image\"}]", "intentionName": "要求退保"}, {"replyText": "您好，查询到您手机尾号${手机尾号!}下，目前有一张在保保单${产品名称!}，这张保单是是免费赠送给您的哦，不会收取任何费用，到期自动失效，请您放心。", "stepDesc": "用户仅有赠险", "stepCode": "Z24", "intentionCode": "surrender_insurance", "nodeDesc": "仅有赠险：赠险无需费用告知&索要扣费截图", "_score": 0.028170876, "编号": "5f32311c-1dab-4d3e-a6a8-d3fb5a4d08d2", "extraInfo": "[<#if policyFileList?? && policyFileList?size gt 0>{\"content\": \"我把保单给您发过去看一下，福利保单也是可以为您提供基本保障的，您如果后续有其他疑问，如理赔流程等，可以随时咨询我协助您办理哦。\",\"type\": \"text\"},<#list policyFileList as policy>{\"fileUrl\": \"${policy.url}\",\"fileName\": \"${policy.fileName}\",\"type\": \"file\"},</#list></#if>{\"content\":\"如果您在众安经纪有保单扣费的话，可以把保单扣费截图发给我为您查询哦。\",\"type\":\"text\"},{\"imageUrl\":\"https://nmpcdn.zhonganib.com/nmp/1747652691552/163646e934a111f09b79fa4478b5c044.png?&w=750&h=4461\",\"type\":\"image\"}]", "intentionName": "要求退保"}, {"replyText": "进入挽单流程-目标挽单列表仅一张保单:流程结束，需人工处理：2轮挽单后同意保留保单", "stepDesc": "转人工", "stepCode": "T69", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-目标挽单列表仅一张保单:流程结束，2轮挽单后同意保留保单", "_score": 0.028170876, "编号": "5ee415d5-1505-4907-8e9c-fa6a471a50fc", "intentionName": "要求退保"}, {"replyText": "进入挽单流程-目标挽单列表仅一张保单:流程结束，需人工处理：1轮挽单后同意保留保单", "stepDesc": "转人工", "stepCode": "T68", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-目标挽单列表仅一张保单:流程结束，1轮挽单后同意保留保单", "_score": 0.028170876, "编号": "4a5053df-0527-4493-bd4b-9f2058628d03", "intentionName": "要求退保"}, {"replyText": "进入挽单流程-目标挽单列表多张保单:流程结束，需人工处理：3轮挽单后同意保留全部保单", "stepDesc": "转人工", "stepCode": "T65", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-目标挽单列表多张保单:流程结束，3轮挽单后同意保留全部保单", "_score": 0.028170876, "编号": "91c64033-5734-43e2-8682-ca005451d181", "intentionName": "要求退保"}, {"replyText": "保险名称显示（福利版）字样的产品是免费的赠险，请您放心，赠险是不会产生任何扣费，到期自动就失效了，无需您操作。", "stepDesc": "用户仅有赠险-无需费用二次告知", "stepCode": "Z29", "maxRounds": "3", "intentionCode": "surrender_insurance", "nodeDesc": "仅有赠险：赠险无需费用告知&索要扣费截图（未提供，不相信免费）-无需费用二次告知", "_score": 0.011976191, "编号": "6e61fff4-59ef-47d9-bf7c-5e58001a30c4", "intentionName": "要求退保"}, {"replyText": "好的${投保人姓名!}<#if user_gender??><#if user_gender=='1'>先生<#else>女士</#if><#else></#if>，那如果后续有任何问题您可以随时联系我，感谢您对众安经纪的支持🌹", "stepDesc": "用户无保单确认服务结束话术", "stepCode": "C07", "intentionCode": "surrender_insurance", "nodeDesc": "用户名下无单：无保单告知&索要扣费截图（未提供，认可告知）-结束语下发", "_score": 0.011976191, "编号": "33db87c9-7ed1-4251-8564-e8f5dd9de219", "intentionName": "要求退保"}, {"replyText": "非常抱歉，赠险我们是无法操作退保的，如果您想取消可以致电保司进行操作哦。\n${保司名称!} 客服电话：${客服电话!}", "stepDesc": "赠险退保解释一次", "stepCode": "E03", "intentionCode": "surrender_insurance", "nodeDesc": "仅有赠险：赠险无需费用告知&索要扣费截图（未提供，要求退保赠险/不相信免费）-无费用+无需退保告知/无需费用二次告知（仍要退保）-赠险退保解释一次", "_score": 0.011976191, "编号": "c1cb5667-c53b-4c20-9568-99b1209fde15", "intentionName": "要求退保"}, {"replyText": "好的${投保人姓名!}<#if user_gender??><#if user_gender=='1'>先生<#else>女士</#if><#else></#if>，那如果后续有任何问题您可以随时联系我，感谢您对众安经纪的支持🌹", "stepDesc": "用户仅有赠险-用户确认", "stepCode": "C17", "intentionCode": "surrender_insurance", "nodeDesc": "仅有赠险：赠险无需费用告知&索要扣费截图（未提供，认可告知）-结束语下发", "_score": 0.011976191, "编号": "21e9a506-f55f-4d57-89f4-3c435607be5d", "intentionName": "要求退保"}, {"replyText": "您截图的保单是<#if invalid_corps??><#list invalid_corps as corp>${corp}、</#list></#if>的保单，我们是众安经纪的客服，不是同一家公司。如果您想取消他们公司的保单，您可以拨打他们的客服电话进行咨询哦。", "stepDesc": "用户仅有赠险-全部非我司-多单", "stepCode": "Z26", "intentionCode": "surrender_insurance", "nodeDesc": "仅有赠险：赠险无需费用告知&索要扣费截图（全部非我司扣费截图）-司外保单告知", "_score": 0.011976191, "编号": "899427f9-b330-4411-ab33-935f24a28f1c", "intentionName": "要求退保"}, {"replyText": "${投保人姓名!}<#if user_gender??><#if user_gender=='1'>先生<#else>女士</#if><#else></#if>您好呀😃~ 麻烦您用投保手机号登录一下哦，我马上帮您查询保单", "stepDesc": "引导用户核身", "stepCode": "G01", "intentionCode": "surrender_insurance", "nodeDesc": "引导登录：引导核身", "_score": 0.011976191, "编号": "297ac735-3610-4e36-b186-a22ff12d57fa", "extraInfo": "[{\"content\":\"那辛苦您用投保手机号登录一下，我帮您查询下。\",\"type\":\"text\"},{\"miniChannel\":\"JJ_MINIAPP_ZAB\",\"miniImageUrl\":\"https://nmpcdn.zhonganib.com/nmp/1713340631738/18d84237fc9011ee8aabcee78f94c7cc.png?w=2084&h=2084\",\"miniPath\":\"/pages/login/index.html?channelNo=480003&channelCode=07qwfx001\",\"miniTitle\":\"请使用投保手机号登录\",\"type\":\"miniprogrampage\"}]", "intentionName": "退保"}, {"replyText": "在现代生活里，风险无处不在。给自己配置一份意外险，就像筑起一道坚固的屏障🛡️，一旦意外发生，能获得赔付，有效减轻家庭的负担与压力，真的很有必要哦～", "stepDesc": "产品讲解（责任不清楚）--意外险", "stepCode": "Z15", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-目标挽单列表仅一张保单：险类：意外-无损第一轮", "_score": 0.011976191, "编号": "e27b92ee-540c-4bad-ba7f-7059c98dc792", "intentionName": "要求退保"}, {"replyText": "您点击下面给您发送的“极速退保”链接，请您务必准确选择您要退保的保单，确认一下退费金额，小心误操作～如果有疑问的话随时联系我们。", "stepDesc": "确认退保 发送链接--单张", "stepCode": "S08", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-目标挽单列表仅一张保单：险类：${险类}-无损第一轮（未同意）-无损第二轮（未同意）-退保损失讲解（未同意）-权益挽单（未同意）-解停挽单（未同意）-挽单失败确认保单（未否认）-发送退保链接", "_score": 0.011976191, "编号": "728ac160-f4bb-492c-b52b-82c263d73bea", "extraInfo": "[{\"miniChannel\":\"JJ_MINIAPP_ZAB\",\"miniContent\":\"极速退保通道\",\"miniImageUrl\":\"https://nmpcdn.zhonganib.com/nmp/1729840628541/2553bf1292a111efb7406ae51353cbbb.png?&w=700&h=592\",\"miniPath\":\"/pages/finance/commonTransfer/index?pageType=dischargeList&operateCode=${surrenderCode}\",\"miniTitle\":\"极速退保通道\",\"type\":\"miniprogrampage\"},{\"imageUrl\":\"https://nmpcdn.zhonganib.com/nmp/1747652416863/727c0f0c34a011f0a5c6b60542ca4545.png?&w=750&h=2815\",\"type\":\"image\"}]", "intentionName": "要求退保"}, {"replyText": "您点击下面给您发送的“极速退保”链接，请您务必准确选择您要退保的保单，确认一下退费金额，小心误操作～如果有疑问的话随时联系我们。", "stepDesc": "确认退保 发送链接--多张", "stepCode": "S07", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-目标挽单列表多张保单：无损第一轮（全部不同意）-无损第二轮（全部不同意）-退保损失讲解（全部不同意）-权益挽单（全部不同意）-解停挽单（全部不同意）-挽单失败确认保单（未否认）-发送退保链接", "_score": 0.011976191, "编号": "bfb89029-375c-40ed-a2db-a8f97310cf08", "extraInfo": "[{\"miniChannel\":\"JJ_MINIAPP_ZAB\",\"miniContent\":\"极速退保通道\",\"miniImageUrl\":\"https://nmpcdn.zhonganib.com/nmp/1729840628541/2553bf1292a111efb7406ae51353cbbb.png?&w=700&h=592\",\"miniPath\":\"/pages/finance/commonTransfer/index?pageType=dischargeList&operateCode=${surrenderCode}\",\"miniTitle\":\"极速退保通道\",\"type\":\"miniprogrampage\"},{\"imageUrl\":\"https://nmpcdn.zhonganib.com/nmp/1747652416863/727c0f0c34a011f0a5c6b60542ca4545.png?&w=750&h=2815\",\"type\":\"image\"}]", "intentionName": "要求退保"}, {"replyText": "您好，查询到您当前在众安优保没有保单哦，不会收取任何费用，请您放心。", "stepDesc": "无保单告知", "stepCode": "Z07", "intentionCode": "surrender_insurance", "nodeDesc": "用户名下无单：无保单告知&索要扣费截图", "_score": 0.011976191, "编号": "42f49968-4c6e-4672-9af1-45d2bda6a2f2", "extraInfo": "[{\"content\":\"如果您在众安经纪有保单扣费的话，可以把保单扣费截图发给我为您查询哦。\",\"type\":\"text\"},{\"imageUrl\":\"https://nmpcdn.zhonganib.com/nmp/1747652691552/163646e934a111f09b79fa4478b5c044.png?&w=750&h=4461\",\"type\":\"image\"}]", "intentionName": "要求退保"}, {"replyText": "好的${投保人姓名!}<#if user_gender??><#if user_gender=='1'>先生<#else>女士</#if><#else></#if>，那如果后续有任何问题您可以随时联系我，感谢您对众安经纪的支持🌹", "stepDesc": "用户仅有赠险-用户确认", "stepCode": "C19", "intentionCode": "surrender_insurance", "nodeDesc": "仅有赠险：赠险无需费用告知&索要扣费截图（未提供，要求退保赠险/不相信免费）-无费用+无需退保告知/无需费用二次告知（仍要退保）-赠险退保解释一次（用户认可）-赠险保留结束语下发", "_score": 0.011976191, "编号": "146a7eb9-f540-4a7d-878d-9caccc6de83d", "intentionName": "要求退保"}, {"replyText": "<#if policyList??>\n您好，查询到您手机尾号${手机尾号!}下有这几份保单\n<#list policyList as policy>\n【${policy.产品名称!}】<#if policy_has_next>，</#if>\n</#list>，\n首次投保保费分别为\n<#list policyList as policy>\n${policy.首月扣费金额!}元<#if policy_has_next>，</#if>\n</#list>，\n成功投保后的次月升级保障后保费分别为\n<#list policyList as policy>\n${policy.次月保费!}元<#if policy_has_next>，</#if>\n</#list>\n<#else>\n您好，查询到您手机尾号xxx下有这几份保单，首次投保保费分别为xxx元，成功投保后的次月升级保障后保费分别为xxx元。</#if>\n您是要退这几份保单吗？", "stepDesc": "发送确认保单话术", "stepCode": "C12", "intentionCode": "surrender_insurance", "nodeDesc": "确认保单-用户名下多单-不满足挽单条件：下发确认保单", "_score": 0.011976191, "编号": "5868b23d-bea0-4747-bfc7-997628ec9d21", "intentionName": "要求退保"}, {"replyText": "您申请退保的保单信息如下： \n1.产品名称：${产品名称!} \n✅被保人：${被保人姓名!} \n✅交费金额：${月保费!}元/月（年交：${年保费!}元/年）\n✅预计退费金额：${退保金额!}\n您看是这份吗", "stepDesc": "进入挽单流程-目标挽单列表仅一张保单：险类：无损第一轮（未同意）-无损第二轮（未同意）-退保损失讲解（未同意）-权益挽单（未同意）-解停挽单（未同意）-挽单失败确认保单", "stepCode": "C13", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-目标挽单列表仅一张保单：险类：${险类}-无损第一轮（未同意）-无损第二轮（未同意）-退保损失讲解（未同意）-权益挽单（未同意）-解停挽单（未同意）-挽单失败确认保单", "_score": 0.011976191, "编号": "1577a478-55f4-4a7d-b761-0127e9005a66", "intentionName": "要求退保"}, {"replyText": "您是否还记得大概扣了多少费用，或者您购买的产品名称呢？", "stepDesc": "确认扣费金额/保单名称", "stepCode": "C03", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-定位保单-无历史记录：索要扣费截图（未提供：未提及全退）-确认保单截图（未提供：未提及全退）-确认扣费金额/保单名称", "_score": 0.011976191, "编号": "6fbba85f-32ff-4abc-b8bf-41f02bad6491", "intentionName": "要求退保"}, {"replyText": "您这张保单是${保司名称!}的保单哦，我们是众安经纪的客服，跟${保司名称!}不是同一家公司。如果您想取消他们公司的保单，您可以拨打他们的客服电话进行咨询哦。", "stepDesc": "用户仅有赠险-全部非我司", "stepCode": "Z25", "intentionCode": "surrender_insurance", "nodeDesc": "仅有赠险：赠险无需费用告知&索要扣费截图（全部非我司扣费截图）-司外保单告知", "_score": 0.011976191, "编号": "a8a8d1e8-f1f2-45c3-a9a2-dfa4d019436b", "intentionName": "要求退保"}, {"replyText": "给您解释下哈，保险生效后已提供实际保障，中途退保会造成已缴费用损失。不过别担心，未保障期间的费用会退还给您的🤗。", "stepDesc": "解释有损退", "stepCode": "E02", "intentionCode": "surrender_insurance", "nodeDesc": "退保-目标退保多单：引导用户确认退保金额（不认可）-解释有损退", "_score": 0.011976191, "编号": "d0ca176a-2ac1-476f-b4da-6af9fa0089e5", "intentionName": "要求退保"}, {"replyText": "您这份【${产品名称!}】现在为您受理退保退费，您的保单当前预估应退保费为 ${退保金额!} 元\n您确认现在退保吗?", "stepDesc": "用户确认退保金额", "stepCode": "C09", "intentionCode": "surrender_insurance", "nodeDesc": "退保-目标退保一单：引导用户确认退保金额", "_score": 0.011976191, "编号": "9f1fee5a-1ce3-4da3-9af1-54c6340ae005", "intentionName": "要求退保"}, {"replyText": "给您解释下哈，保险生效后已提供实际保障，中途退保会造成已缴费用损失。不过别担心，未保障期间的费用会退还给您的🤗。", "stepDesc": "解释有损退", "stepCode": "E01", "intentionCode": "surrender_insurance", "nodeDesc": "退保-目标退保一单：引导用户确认退保金额（不认可）-解释有损退", "_score": 0.011976191, "编号": "4795422e-769a-4b39-b3a8-2b1fa7d1c565", "intentionName": "要求退保"}, {"replyText": "麻烦您把需要退的保单扣费截图发给我哦🧐。您可通过以下方式查到扣费记录～", "stepDesc": "发送索要扣费截图话术", "stepCode": "A05", "intentionCode": "surrender_insurance", "nodeDesc": "定位保单-用户名下<#if insurance_size gt 1>多单<#else>一单</#if>-不满足挽单条件：索要扣费截图", "_score": 0.011976191, "编号": "62fd195a-4a5c-4d9a-bfb0-e8bf5733d1ca", "extraInfo": "[{\"imageUrl\":\"https://nmpcdn.zhonganib.com/nmp/1747652691552/163646e934a111f09b79fa4478b5c044.png?&w=750&h=4461\",\"type\":\"image\"}]", "intentionName": "要求退保"}, {"replyText": "您直接点击下方 “查保单” 工具，就能查询保单啦🎈~ 操作很方便！", "stepDesc": "不挽单定位保单小程序查询", "stepCode": "S05", "intentionCode": "surrender_insurance", "nodeDesc": "定位保单-用户名下<#if insurance_size gt 1>多单<#else>一单</#if>-不满足挽单条件：索要扣费截图（未提供）-确认扣费金额/保单名称（未提供）-查保单工具/截图", "_score": 0.011976191, "编号": "d8ddf6b8-7991-48c4-9531-4d23b690f5cc", "extraInfo": "[{\"miniChannel\":\"JJ_MINIAPP_ZAB\",\"miniImageUrl\":\"https://nmpcdn.zhonganib.com/nmp/1748482297973/aa2c1ad63c2c11f0b74242ea753d5cbf.png?&w=700&h=592\",\"miniPath\":\"/pages/policy/index.html?bizOrigin=gzh&channelNo=480003&channelCode=07qwfx001&resourceNo=qyfx_bdcx_fxzsk0725&startNo=qyfx_bdcx_fxzsk0725\",\"miniTitle\":\"查保单小程序\",\"type\":\"miniprogrampage\"}]", "intentionName": "要求退保"}, {"replyText": "需人工处理：存在未匹配到的保单", "stepDesc": "存在未匹配到的保单", "stepCode": "T23", "intentionCode": "surrender_insurance", "nodeDesc": "定位保单-用户名下<#if insurance_size gt 1>多单<#else>一单</#if>-不满足挽单条件：索要扣费截图（提供）-转人工，存在ocr识别与名下保单不匹配", "_score": 0.011976191, "编号": "bf6c6b4f-18fe-41a9-8b62-3c8718d77c18", "intentionName": "要求退保"}, {"replyText": "需人工处理：非我司扣费截图，用户要求退保", "stepDesc": "转人工", "stepCode": "T11", "intentionCode": "surrender_insurance", "nodeDesc": "仅有赠险：赠险无需费用告知&索要扣费截图（全部非我司扣费截图）-司外保单告知（要求退保）-转人工，非我司扣费截图要求退保", "_score": 0.011976191, "编号": "3227cee1-a1bc-40f1-ae73-820b9951ecd4", "intentionName": "要求退保"}, {"replyText": "需人工处理：存在非我司扣费截图", "stepDesc": "存在非我司扣费截图", "stepCode": "T22", "intentionCode": "surrender_insurance", "nodeDesc": "定位保单-用户名下<#if insurance_size gt 1>多单<#else>一单</#if>-不满足挽单条件：索要扣费截图（提供）-转人工，存在非我司扣费截图", "_score": 0.011976191, "编号": "cc991841-ee6f-4e89-b084-b241cb3b0200", "intentionName": "要求退保"}, {"replyText": "需人工处理：存在ocr识别与名下保单不匹配", "stepDesc": "转人工", "stepCode": "T61", "intentionCode": "surrender_insurance", "nodeDesc": "定位保单-用户名下<#if insurance_size gt 1>多单<#else>一单</#if>-不满足挽单条件：索要扣费截图（未提供）-确认扣费金额/保单名称（未提供）-查保单工具/截图（提供）-转人工，存在ocr识别与名下保单不匹配", "_score": 0.011976191, "编号": "810b1c18-9d06-434c-b801-4905fdbde96b", "intentionName": "要求退保"}, {"replyText": "需人工处理：存在ocr识别失败", "stepDesc": "无我司扣费信息", "stepCode": "T33", "intentionCode": "surrender_insurance", "nodeDesc": "定位保单-用户名下<#if insurance_size gt 1>多单<#else>一单</#if>-不满足挽单条件：索要扣费截图（提供）-转人工，存在ocr识别失败", "_score": 0.011976191, "编号": "2cf0f5d6-ae3c-4145-9a2c-1e8c061f69b9", "intentionName": "要求退保"}, {"replyText": "需人工处理：引导自行操作后，用户未操作，再次进线", "stepDesc": "转人工", "stepCode": "T51", "intentionCode": "surrender_insurance", "nodeDesc": "退保-目标退保多单：引导用户确认退保金额（认可）-发送退保工具（未操作）-引导自行操作（未操作）-转人工，引导自行操作后用户未操作", "_score": 0.011976191, "编号": "64210eac-671d-4635-a9d2-55a8068f3bd6", "intentionName": "要求退保"}, {"replyText": "需人工处理：用户提供我司截图，但未提供手机号", "stepDesc": "转人工", "stepCode": "T40", "intentionCode": "surrender_insurance", "nodeDesc": "用户名下无单：无保单告知&索要扣费截图（全部我司扣费截图）-其他手机号确认（未提供）-转人工，用户未提供手机号", "_score": 0.011976191, "编号": "f2119bc5-b588-43b9-9036-6a403cb0b302", "intentionName": "要求退保"}, {"replyText": "需人工处理：四轮挽单后用户同意部分保留", "stepDesc": "转人工", "stepCode": "T77", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-目标挽单列表多张保单：无损第一轮（全部不同意）-无损第二轮（全部不同意）-退保损失讲解（全部不同意）-权益挽单（部分同意）-转人工，复杂场景：权益挽单，用户同意保留部分保单，需人工区分", "_score": 0.011976191, "编号": "9df5c09d-735e-4445-b763-c8f984ab70d7", "intentionName": "要求退保"}, {"replyText": "需人工处理：五轮挽单后用户同意部分保留", "stepDesc": "转人工", "stepCode": "T78", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-目标挽单列表多张保单：无损第一轮（全部不同意）-无损第二轮（全部不同意）-退保损失讲解（全部不同意）-权益挽单（全部不同意）-解停挽单（部分同意）-转人工，复杂场景：解停挽单，用户同意保留部分保单，需人工区分", "_score": 0.011976191, "编号": "add12f96-cb8e-49fa-b161-e896774b2ca6", "intentionName": "要求退保"}, {"replyText": "需人工处理：三轮挽单后用户同意部分保留", "stepDesc": "转人工", "stepCode": "T76", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-目标挽单列表多张保单：无损第一轮（全部不同意）-无损第二轮（全部不同意）-退保损失讲解（部分同意）-转人工，复杂场景：无损三挽，用户同意保留部分保单，需人工区分", "_score": 0.011976191, "编号": "c9bf624e-a4d7-47b6-897e-7f279316980f", "intentionName": "要求退保"}, {"replyText": "需人工处理：用户咨询其他问题", "stepDesc": "转人工", "stepCode": "T15", "intentionCode": "surrender_insurance", "nodeDesc": "仅有赠险：赠险无需费用告知&索要扣费截图（未提供，要求退保赠险/不相信免费）-无费用+无需退保告知/无需费用二次告知（仍要退保）-赠险退保解释一次（其他）-转人工，用户咨询其他", "_score": 0.011976191, "编号": "72988f8e-edd9-47b6-a119-bff6c2bcc2bc", "intentionName": "要求退保"}, {"replyText": "需人工处理：无单且未提供其他截图，且用户咨询其他", "stepDesc": "转人工", "stepCode": "T39", "intentionCode": "surrender_insurance", "nodeDesc": "用户名下无单：无保单告知&索要扣费截图（未提供，表述其他）-转人工，用户咨询其他", "_score": 0.011976191, "编号": "4de856f2-1fba-4902-91ef-507b0ee886e1", "intentionName": "要求退保"}, {"replyText": "需人工处理：用户提供我司截图，但未提供手机号", "stepDesc": "转人工", "stepCode": "T19", "intentionCode": "surrender_insurance", "nodeDesc": "仅有赠险：赠险无需费用告知&索要扣费截图（全部我司扣费截图）-其他手机号确认（未提供）-转人工，用户未提供手机号", "_score": 0.011976191, "编号": "4826bb93-5bce-4ed6-a6cc-bcc5ec80edcb", "intentionName": "要求退保"}, {"replyText": "需人工处理：提供保单截图--存在未匹配到的保单", "stepDesc": "转人工", "stepCode": "T26", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-定位保单-无历史记录：索要扣费截图（未提供：未提及全退）-确认保单截图（提供）-转人工，存在ocr识别与名下保单不匹配", "_score": 0.011976191, "编号": "296bfaa1-2bf2-4771-a81c-44b7fc386f1c", "intentionName": "要求退保"}, {"replyText": "需人工处理：存在ocr识别与名下保单不匹配", "stepDesc": "进入挽单流程-定位保单-T-1有历史记录：T-1内有扣费截图-转人工，存在ocr识别与名下保单不匹配", "stepCode": "T02", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-定位保单-T-1有历史记录：T-1内有扣费截图-转人工，存在ocr识别与名下保单不匹配", "_score": 0.011976191, "编号": "34cbe9f1-9ba3-4051-a212-9938a4664edb", "intentionName": "要求退保"}, {"replyText": "需人工处理：存在ocr识别失败", "stepDesc": "进入挽单流程-定位保单-有历史记录：T-1内无扣费截图-T-1内有保单截图-转人工，存在ocr识别失败", "stepCode": "T03", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-定位保单-有历史记录：T-1内无扣费截图-T-1内有保单截图-转人工，存在ocr识别失败", "_score": 0.011976191, "编号": "28e0dbb5-91c7-448a-87be-e58f139eb848", "intentionName": "要求退保"}, {"replyText": "您好，查询到您手机尾号${手机尾号!}下，目前有${user_free_insurance_size}张在保保单，分别是：\n<#if policyList??><#list policyList as policy>${policy_index + 1}.产品名称：${policy.产品名称!}</#list></#if>\n这几份都是免费赠送给您的哦，不会收取任何费用，到期自动失效，请您放心。", "stepDesc": "用户仅有赠险-多单", "stepCode": "Z27", "intentionCode": "surrender_insurance", "nodeDesc": "仅有赠险：赠险无需费用告知&索要扣费截图", "_score": 0.011976191, "编号": "a492d828-1a46-40d1-9dd3-6d98bc84118f", "extraInfo": "[<#if policyFileList?? && policyFileList?size gt 0>{\"content\": \"我把保单给您发过去看一下，福利保单也是可以为您提供基本保障的，您如果后续有其他疑问，如理赔流程等，可以随时咨询我协助您办理哦。\",\"type\": \"text\"},<#list policyFileList as policy>{\"fileUrl\": \"${policy.url}\",\"fileName\": \"${policy.fileName}\",\"type\": \"file\"},</#list></#if>{\"content\":\"如果您在众安经纪有保单扣费的话，可以把保单扣费截图发给我为您查询哦。\",\"type\":\"text\"},{\"imageUrl\":\"https://nmpcdn.zhonganib.com/nmp/1747652691552/163646e934a111f09b79fa4478b5c044.png?&w=750&h=4461\",\"type\":\"image\"}]", "intentionName": "要求退保"}, {"replyText": "您申请退保的保单信息如下：\n<#if policyList??>\n<#list policyList as policy> \n${policy_index + 1}. 产品名称：${policy.产品名称!} \n✅被保人：${policy.被保人姓名!}\n✅交费金额：${policy.月保费!}元/月（年交：${policy.年保费!}元/年）\n✅预计退费金额：${policy.退保金额!}\n</#list>\n</#if> \n您确认一下是否退保", "stepDesc": "退保确认--多张", "stepCode": "C14", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-目标挽单列表多张保单：无损第一轮（全部不同意）-无损第二轮（全部不同意）-退保损失讲解（全部不同意）-权益挽单（全部不同意）-解停挽单（全部不同意）-挽单失败确认保单", "_score": 0.011976191, "编号": "6fbd8cf5-2d84-40cd-9e25-6be217b1a7d0", "intentionName": "要求退保"}, {"replyText": "进入挽单流程-目标挽单列表仅一张保单:流程结束，需人工处理：4轮挽单后同意保留保单", "stepDesc": "转人工", "stepCode": "T71", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-目标挽单列表仅一张保单:流程结束，4轮挽单后同意保留保单", "_score": 0.011976191, "编号": "feedf64b-7e01-4f19-bc73-212846e4482a", "intentionName": "要求退保"}, {"replyText": "需人工处理：存在非我司扣费截图", "stepDesc": "挽单定位保单--存在非我司扣费截图", "stepCode": "T80", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-定位保单-T-1有历史记录：T-1内有扣费截图-转人工，存在非我司扣费截图", "_score": 0.011976191, "编号": "53502815-ae3d-43cf-bdca-45944866c2c7", "intentionName": "要求退保"}, {"replyText": "好的${投保人姓名!}<#if user_gender??><#if user_gender=='1'>先生<#else>女士</#if><#else></#if>，那如果后续有任何问题您可以随时联系我，感谢您对众安经纪的支持🌹", "stepDesc": "用户仅有赠险-全部非我司-用户确认", "stepCode": "C16", "intentionCode": "surrender_insurance", "nodeDesc": "仅有赠险：赠险无需费用告知&索要扣费截图（全部非我司扣费截图）-司外保单告知（认可）-结束语下发", "_score": 0.0074906712, "编号": "3db80a3f-5335-47bf-972d-f0205de89117", "intentionName": "要求退保"}, {"replyText": "实在抱歉，赠险我们是没有权限帮您退保的，辛苦您拨打上面的保司客电话，联系保司进行操作哦。", "stepDesc": "赠险退保解释两次", "stepCode": "E05", "intentionCode": "surrender_insurance", "nodeDesc": "仅有赠险：赠险无需费用告知&索要扣费截图（未提供，要求退保赠险/不相信免费）-无费用+无需退保告知/无需费用二次告知（仍要退保）-赠险退保解释一次（仍要退保）-赠险退保解释二次", "_score": 0.0074906712, "编号": "9562d787-097c-408b-acfd-20478debab32", "intentionName": "要求退保"}, {"replyText": "辛苦您回忆下当时投保的手机号， 我为您查询下， 当前${手机尾号!}的手机尾号下没有查询到呢。", "stepDesc": "无保单未登录-通过其他手机号确认", "stepCode": "C08", "intentionCode": "surrender_insurance", "nodeDesc": "用户名下无单：无保单告知&索要扣费截图（全部我司扣费截图）-其他手机号确认", "_score": 0.0074906712, "编号": "ac46889b-4fb4-45dd-9b35-3063c4b3470e", "intentionName": "要求退保"}, {"replyText": "亲☺️，非常抱歉，给您带来不好的体验，请问您是什么原因退保呢？🤔", "stepDesc": "单张保单-询问退保原因", "stepCode": "A03", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-目标挽单列表仅一张保单：询问退保原因", "_score": 0.0074906712, "编号": "10227dab-7a12-4a41-a36d-1ca40f4c14c1", "intentionName": "要求退保"}, {"replyText": "亲😃，这款医疗险对年龄和健康状况确实有要求哦🧐。它可不是只要有钱，啥时候都能投保的。要是哪天咱们不符合投保条件了，即便有再多钱，想投也难啦😟。\n您看，您这份保险已经生效。大家都知道，买保险这件事，宜早不宜迟呀！早投保，早安心，也能更早享受保障～", "stepDesc": "产品讲解（责任不清楚）--医疗险", "stepCode": "Z18", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-目标挽单列表仅一张保单：险类：医疗-无损第一轮", "_score": 0.0074906712, "编号": "281dc557-00b3-44da-a0a9-20d775fd0157", "intentionName": "要求退保"}, {"replyText": "亲🧐，意外险的性价比那可是相当高👍！您想想，一年交的保费可能也就一顿聚餐的钱🍱，却能在一整年里为您提供全方位的意外保障。无论是日常生活中的磕磕碰碰，还是外出旅行、工作时的意外风险（责任内）它都能管。一旦出险，赔付金额往往是保费的好多倍，实实在在以小博大。这份用小钱换大保障的实惠，放弃多可惜呀🥺，继续留着它为您保驾护航多好。", "stepDesc": "保险价值（金额疑问）--意外险", "stepCode": "Z10", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-目标挽单列表仅一张保单：险类：意外-无损第一轮（未同意）-无损第二轮", "_score": 0.0074906712, "编号": "0c5ed7f4-bfcc-477e-8ea2-8b96b67f9b29", "intentionName": "要求退保"}, {"replyText": "亲😃，仔细算算，房屋财产险真的很划算。一年保费没多少，相比房屋价值和潜在损失，只是很小一部分。就像给房屋买了份 “平安险”，花小钱换大安心。而且，咱们理赔服务超高效，真遇到事儿，能快速赔付，帮您减少经济负担，退保实在可惜啦🥺。", "stepDesc": "保险价值（金额疑问）--家财险", "stepCode": "Z12", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-目标挽单列表仅一张保单：险类：家财-无损第一轮（未同意）-无损第二轮", "_score": 0.0074906712, "编号": "3dfecf3b-2458-4322-947b-8b4a555b4361", "intentionName": "要求退保"}, {"replyText": "亲😃，医疗险真别轻易退呀！您也清楚，现在去医院随便看个病，费用都不低，要是遇上大病，那更是天文数字😟。像住院费、检查费、药品费，一路飙升，分分钟掏空家庭积蓄💰。咱们的医疗险，能在您需要的时候，报销大部分费用，大大减轻经济压力。每年交的保费，跟可能面临的高额医疗费相比，简直微不足道。有它在，看病不愁钱，退保多可惜，留着才踏实🤗。", "stepDesc": "保险价值（金额疑问）--医疗险", "stepCode": "Z13", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-目标挽单列表仅一张保单：险类：医疗-无损第一轮（未同意）-无损第二轮", "_score": 0.0074906712, "编号": "df7f5d89-5db4-4c89-82b6-752c0dfae933", "intentionName": "要求退保"}, {"replyText": "建议您可以关闭自动扣费，保单到下一期扣费时间就不会自动扣费了，并且您已交保费也可以保障到宽限期结束，我把关闭自动扣费的链接发您，您在上面操作一下就可以了", "stepDesc": "解停挽单--单张", "stepCode": "Z23", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-目标挽单列表仅一张保单：险类：${险类}-无损第一轮（未同意）-无损第二轮（未同意）-退保损失讲解（未同意）-权益挽单（未同意）-解停挽单", "_score": 0.0074906712, "编号": "7bc9185e-e1ba-42f7-b903-7312972a036d", "extraInfo": "[{\"miniChannel\":\"JJ_MINIAPP_ZAB\",\"miniImageUrl\":\"https://nmpcdn.zhonganib.com/nmp/1735009055453/d3d57868c1a211ef8cf75615d1d5adff.png?&w=700&h=592\",\"miniPath\":\"/pages/finance/commonTransfer/index?pageType=closeRenewalList&operateCode=${cancelRenewalCode}\",\"miniTitle\":\"点击操作取消续费\",\"type\":\"miniprogrampage\"},{\"content\":\"温馨提示：关闭自动扣费之后，保单过了交费宽限期没有交上保单会失效，保障权益无法享受，重新投保需要重新计算等待期，您在宽限期内也可以考虑一下，如果想继续享受保障可以点击主动缴费哦。\",\"type\":\"text\"}]", "intentionName": "要求退保"}, {"replyText": "建议您可以关闭自动扣费，保单到下一期扣费时间就不会自动扣费了，并且您已交保费也可以保障到宽限期结束，我把关闭自动扣费的链接发您，您在上面操作一下就可以了", "stepDesc": "解停挽单--多张", "stepCode": "Z22", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-目标挽单列表多张保单：无损第一轮（全部不同意）-无损第二轮（全部不同意）-退保损失讲解（全部不同意）-权益挽单（全部不同意）-解停挽单", "_score": 0.0074906712, "编号": "4af07534-db52-4d7c-887f-5d3632a9d9cf", "extraInfo": "[{\"miniChannel\":\"JJ_MINIAPP_ZAB\",\"miniImageUrl\":\"https://nmpcdn.zhonganib.com/nmp/1735009055453/d3d57868c1a211ef8cf75615d1d5adff.png?&w=700&h=592\",\"miniPath\":\"/pages/finance/commonTransfer/index?pageType=closeRenewalList&operateCode=${cancelRenewalCode}\",\"miniTitle\":\"点击操作取消续费\",\"type\":\"miniprogrampage\"},{\"content\":\"温馨提示：关闭自动扣费之后，保单过了交费宽限期没有交上保单会失效，保障权益无法享受，重新投保需要重新计算等待期，您在宽限期内也可以考虑一下，如果想继续享受保障可以点击主动缴费哦。\",\"type\":\"text\"}]", "intentionName": "要求退保"}, {"replyText": "亲😃，重疾险别轻易退！如今重疾高发，一旦患病，治疗、康复等费用高昂，家庭不堪重负😟。而重疾险确诊即赔，可以有钱治病，增加治愈希望🎗️。每年保费不多，却能守护全家未来，退保可惜啦🤗", "stepDesc": "保险价值（金额疑问）--重疾险", "stepCode": "Z11", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-目标挽单列表仅一张保单-险类：重疾-无损第一轮（未同意）-无损第二轮", "_score": 0.0074906712, "编号": "4c40c71b-1fd5-4a71-b64a-52831bbf178d", "intentionName": "要求退保"}, {"replyText": "退保的话您就没有现在这份保险保障啦。要是之后想重新投保，保险公司会按您当时实际年龄算保费，年龄越大，保费往往越高呢。而且要是重新投健康险，还得按重新投保时间重新算等待期。您再慎重考虑考虑呀🤗。", "stepDesc": "退保损失挽单--多张", "stepCode": "Z20", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-目标挽单列表多张保单：无损第一轮（全部不同意）-无损第二轮（全部不同意）-退保损失讲解", "_score": 0.0074906712, "编号": "c57ed634-1f80-45e5-9c32-6de4f3322a21", "intentionName": "要求退保"}, {"replyText": "因为您已投保，是众安优保的尊享会员，看您还没有领取【免费体检】和【门急诊赠险】等会员权益，我这边发送给您看一下，性价比还蛮高的，如果您觉得不合适可以再申请退保，您看可以吗？", "stepDesc": "权益挽单--多张（四轮挽单）", "stepCode": "Z01", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-目标挽单列表多张保单：无损第一轮（全部不同意）-无损第二轮（全部不同意）-退保损失讲解（全部不同意）-权益挽单", "_score": 0.0074906712, "编号": "d95c1ada-3123-4b69-b974-b785ab00871e", "extraInfo": "[{\"newsDescription\":\"点击领取\",\"newsTitle\":\"门急诊赠险权益已送达\",\"newsUrl\":\"https://nmpcdn.zhonganib.com/nmp/1748420220262/20fa0b953b9c11f09d5096b2bc6a2915.png?&w=200&h=200\",\"type\":\"news\"},{\"newsDescription\":\"点击领取\",\"newsTitle\":\"免费体检权益已送达\",\"newsUrl\":\"https://nmpcdn.zhonganib.com/nmp/1748420220262/20fa0b953b9c11f09d5096b2bc6a2915.png?&w=200&h=200\",\"type\":\"news\"}]", "intentionName": "要求退保"}, {"replyText": "您选的这份保单性价比简直绝了👍！保费低，保额却很高，还能和社保相互补充，保障全面得没话说，理赔也超方便。好多客户都对它特别满意～所以呀，真心建议您先把它了解透彻，再做决定也不迟🤗。", "stepDesc": "多张--保险价值（金额疑问）", "stepCode": "Z14", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-目标挽单列表多张保单：无损第一轮（全部不同意）-无损第二轮", "_score": 0.0074906712, "编号": "b6b264f4-798f-438f-a76b-7a9823f54b30", "intentionName": "要求退保"}, {"replyText": "或者您看一下有没有保单页面的截图我来帮您核实一下~😃", "stepDesc": "进入挽单流程-定位保单-无历史记录：索要扣费截图（未提供：未提及全退）-确认保单截图", "stepCode": "C05", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-定位保单-无历史记录：索要扣费截图（未提供：未提及全退）-确认保单截图", "_score": 0.0074906712, "编号": "827e209a-1d16-4fec-ac62-2b86f35a4ba1", "intentionName": "要求退保"}, {"replyText": "辛苦您回忆下当时投保的手机号，我为您查询下，当前${手机尾号}的手机尾号下没有查询到呢。", "stepDesc": "引导用户登录无果，索要手机号", "stepCode": "Z09", "intentionCode": "surrender_insurance", "nodeDesc": "用户名下无单：无保单告知&索要扣费截图（未提供，要求退保）-无单二次解释并引导登录（未登录）-其他手机号确认", "_score": 0.0074906712, "编号": "4e828c7d-ddf7-4b58-b30b-0008d683485c", "intentionName": "要求退保"}, {"replyText": "您好😃！经过系统查询，您名下的${产品名称!} 是我们公司特别为您准备的感恩回馈免费福利保障🎁，全程不收取任何费用💰。这份保障的有效期限一直到 ${保险止期!} ，在这段时间内都会为您保驾护航🤗。", "stepDesc": "进入挽单流程-定位保单-无历史记录：索要扣费截图（未提供：未提及全退）-确认保单截图（未提供：未提及全退）-确认扣费金额/保单名称（提供：识别保单均为赠险）-赠险无需费用告知&索要其他扣费截图", "stepCode": "Z04", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-定位保单-无历史记录：索要扣费截图（未提供：未提及全退）-确认保单截图（未提供：未提及全退）-确认扣费金额/保单名称（提供：识别保单均为赠险）-赠险无需费用告知&索要其他扣费截图", "_score": 0.0074906712, "编号": "8c0ef654-42ca-4ae2-b0d1-56f7b84130e0", "intentionName": "要求退保"}, {"replyText": "您可以点击下方 “查保单” 工具，用投保手机号登录，就能查询保单啦。查到后，麻烦把保单信息截图发给我哦，感谢您的配合🤗", "stepDesc": "发送查保单工具，索要截图", "stepCode": "S06", "intentionCode": "surrender_insurance", "nodeDesc": "引导登录：引导核身（未操作）-手机号核身（未提供）-查保单工具/截图", "_score": 0.0074906712, "编号": "0af683f1-8a61-4b4e-8ead-4b5f6942e6fd", "extraInfo": "[{\"miniChannel\":\"JJ_MINIAPP_ZAB\",\"miniImageUrl\":\"https://nmpcdn.zhonganib.com/nmp/1748482297973/aa2c1ad63c2c11f0b74242ea753d5cbf.png?&w=700&h=592\",\"miniPath\":\"/pages/policy/index.html?bizOrigin=gzh&channelNo=480003&channelCode=07qwfx001&resourceNo=qyfx_bdcx_fxzsk0725&startNo=qyfx_bdcx_fxzsk0725\",\"miniTitle\":\"查保单小程序\",\"type\":\"miniprogrampage\"}]", "intentionName": "要求退保"}, {"replyText": "查询到您尾号${手机尾号!}下是没有任何保单的，您是使用其他手机号投保过嘛？", "stepDesc": "无单二次告知", "stepCode": "Z08", "intentionCode": "surrender_insurance", "nodeDesc": "用户名下无单：无保单告知&索要扣费截图（未提供，要求退保）-无单二次解释并引导登录", "_score": 0.0074906712, "编号": "e99b55c3-621b-4d02-9bee-c73938dd1c4f", "extraInfo": "[{\"content\":\"那辛苦您用投保手机号登录一下，我帮您查询下。\",\"type\":\"text\"},{\"miniChannel\":\"JJ_MINIAPP_ZAB\",\"miniImageUrl\":\"https://nmpcdn.zhonganib.com/nmp/1713340631738/18d84237fc9011ee8aabcee78f94c7cc.png?w=2084&h=2084\",\"miniPath\":\"/pages/login/index.html?channelNo=480003&channelCode=07qwfx001\",\"miniTitle\":\"请使用投保手机号登录\",\"type\":\"miniprogrampage\"}]", "intentionName": "要求退保"}, {"replyText": "<#if invalid_corp_set??> 您这张保单是 <#list invalid_corp_set as corp>${corp!}<#if corp_has_next>，</#if></#list> 的保单，我们是众安经纪的客服，跟 <#list invalid_corp_set as corp>${corp!}<#if corp_has_next>，</#if></#list> 不是同一家公司。如果您想取消他们公司的保单，您可以拨打他们的客服电话进行咨询哦。 </#if>", "stepDesc": "无保单-全部非我司", "stepCode": "Z06", "intentionCode": "surrender_insurance", "nodeDesc": "用户名下无单：无保单告知&索要扣费截图（全部非我司扣费截图）-司外保单告知", "_score": 0.0074906712, "编号": "7241ccd8-e36b-41cc-87ba-f6ae822086a5", "intentionName": "要求退保"}, {"replyText": "<#if invalid_corp_set??> 您这张保单是 <#list invalid_corp_set as corp>${corp!}<#if corp_has_next>，</#if></#list> 的保单，我们是众安经纪的客服，跟 <#list invalid_corp_set as corp>${corp!}<#if corp_has_next>，</#if></#list> 不是同一家公司。如果您想取消他们公司的保单，您可以拨打他们的客服电话进行咨询哦。 </#if>", "stepDesc": "无保单-全部非我司", "stepCode": "Z05", "intentionCode": "surrender_insurance", "nodeDesc": "用户名下无单：无保单告知&索要扣费截图（全部非我司扣费截图）-司外保单告知", "_score": 0.0074906712, "编号": "227fd416-a4bf-49e2-afec-d9b36a2e0de1", "intentionName": "要求退保"}, {"replyText": "您好，查询到您手机尾号${手机尾号!}下有一份【${产品名称!}】， 首次投保您支付了${首月扣费金额!}元的，成功投保后的次月开始每月保费为${次月扣费金额!}元。您是要退这张保单吗？", "stepDesc": "发送确认保单话术", "stepCode": "C11", "intentionCode": "surrender_insurance", "nodeDesc": "确认保单-用户名下一单-不满足挽单条件：下发确认保单", "_score": 0.0074906712, "编号": "245d9242-1b2f-4ed6-a58c-bcc2a68a9a8c", "intentionName": "要求退保"}, {"replyText": "或者您看一下有没有保单页面的截图我来帮您核实一下~😃", "stepDesc": "向用户要保单截图", "stepCode": "C02", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-定位保单-无历史记录：索要扣费截图（未提供：未提及全退）-确认保单截图", "_score": 0.0074906712, "编号": "8b238ffe-4169-4bc1-879c-b462d4da4530", "intentionName": "要求退保"}, {"replyText": "非常抱歉，赠险我们是无法操作退保的，如果您想取消可以致电保司进行操作哦。\n<#if policyList??><#list policyList as policy>${policy_index + 1}.${policy.保司名称!} 客服电话：${policy.客服电话!}</#list></#if>", "stepDesc": "赠险退保解释一次-多单", "stepCode": "E04", "intentionCode": "surrender_insurance", "nodeDesc": "仅有赠险：赠险无需费用告知&索要扣费截图（未提供，要求退保赠险/不相信免费）-无费用+无需退保告知/无需费用二次告知（仍要退保）-赠险退保解释一次", "_score": 0.0074906712, "编号": "610c1e60-e4a1-4ee4-99b6-0120e3f5ff46", "intentionName": "要求退保"}, {"replyText": "<#if policyList??>\n<#list policyList as policy>\n${policy_index + 1}.产品名称：【${policy.产品名称!}】现在为您受理退保退费，\n您的保单当前预估应退保费为${policy.退保金额!}元\n</#list>\n<#else>\n产品名称:xxx，现在为您受理退保退费\n您的保单当前预估应退保费为xxx元\n</#if>\n以上都确认退保吗？", "stepDesc": "用户确认退保金额-多单", "stepCode": "C10", "intentionCode": "surrender_insurance", "nodeDesc": "退保-目标退保多单：引导用户确认退保金额", "_score": 0.0074906712, "编号": "b26dbd22-c2cc-4d42-9d7b-23c671a497f8", "intentionName": "要求退保"}, {"replyText": "保险名称显示（福利版）字样的产品是免费的赠险，请您放心，是不会产生任何扣费，到期自动就失效了，是不需要退保的哦。", "stepDesc": "用户仅有赠险-无费用+无需退保告知", "stepCode": "Z28", "intentionCode": "surrender_insurance", "nodeDesc": "仅有赠险：赠险无需费用告知&索要扣费截图（未提供，要求退保赠险）-无费用+无需退保告知", "_score": 0.0074906712, "编号": "6e479ded-abc0-4b9b-827b-b673158894b8", "intentionName": "要求退保"}, {"replyText": "需人工处理：存在与名下保单不匹配", "stepDesc": "转人工", "stepCode": "T58", "intentionCode": "surrender_insurance", "nodeDesc": "定位保单-用户名下<#if insurance_size gt 1>多单<#else>一单</#if>-不满足挽单条件：索要扣费截图（未提供）-确认扣费金额/保单名称（提供）-转人工，存在与名下保单不匹配", "_score": 0.0074906712, "编号": "868774e7-268f-45f9-9d04-9eab1615135d", "intentionName": "要求退保"}, {"replyText": "需人工处理：用户未提供任何信息", "stepDesc": "转人工", "stepCode": "T31", "intentionCode": "surrender_insurance", "nodeDesc": "定位保单-用户名下<#if insurance_size gt 1>多单<#else>一单</#if>-不满足挽单条件：索要扣费截图（未提供）-确认扣费金额/保单名称（未提供）-查保单工具/截图（未提供）-转人工，用户未提供任何信息", "_score": 0.0074906712, "编号": "b431ded8-c981-4c7f-ae15-86ef5886e198", "intentionName": "要求退保"}, {"replyText": "需人工处理：非我司扣费截图，用户咨询其他问题", "stepDesc": "转人工", "stepCode": "T36", "intentionCode": "surrender_insurance", "nodeDesc": "用户名下无单：无保单告知&索要扣费截图（全部非我司扣费截图）-司外保单告知（其他）-转人工，用户咨询其他", "_score": 0.0074906712, "编号": "59a3559c-01d6-40ba-9a49-fd3a88e341ce", "intentionName": "要求退保"}, {"replyText": "需人工处理：用户咨询其他", "stepDesc": "转人工", "stepCode": "T16", "intentionCode": "surrender_insurance", "nodeDesc": "仅有赠险：赠险无需费用告知&索要扣费截图（未提供，要求退保赠险/不相信免费）-无费用+无需退保告知/无需费用二次告知（仍要退保）-赠险退保解释一次（其他）-转人工，用户咨询其他", "_score": 0.0074906712, "编号": "d15048bc-8599-4316-b437-db878e36cea4", "intentionName": "要求退保"}, {"replyText": "需人工处理：用户不认可有损退解释", "stepDesc": "转人工", "stepCode": "T43", "intentionCode": "surrender_insurance", "nodeDesc": "退保-目标退保多单：引导用户确认退保金额（不认可）-解释有损退（不认可）-转人工，用户不认可有损退", "_score": 0.0074906712, "编号": "b267a389-3529-4ac8-a63a-c127b41c71f9", "intentionName": "要求退保"}, {"replyText": "需人工处理：未提供有效登录信息", "stepDesc": "未提供有效登录信息", "stepCode": "T34", "intentionCode": "surrender_insurance", "nodeDesc": "引导登录：引导核身（未操作）-手机号核身（未提供）-查保单工具/截图（未提供）-转人工", "_score": 0.0074906712, "编号": "8b8ffc43-3699-40ca-bf8e-987f40b44411", "intentionName": "要求退保"}, {"replyText": "需人工处理：用户执行完退保操作", "stepDesc": "转人工", "stepCode": "T48", "intentionCode": "surrender_insurance", "nodeDesc": "退保-目标退保一单：引导用户确认退保金额（不认可）-解释有损退（认可）-发送退保工具（操作）-转人工，用户操作退保后再次进线", "_score": 0.0074906712, "编号": "f8b8de0e-5134-473e-8ee0-542892cb69e3", "intentionName": "要求退保"}, {"replyText": "需人工处理：用户咨询其他问题", "stepDesc": "转人工", "stepCode": "T10", "intentionCode": "surrender_insurance", "nodeDesc": "仅有赠险：赠险无需费用告知&索要扣费截图（全部非我司扣费截图）-司外保单告知（其他）-转人工，用户咨询其他", "_score": 0.0074906712, "编号": "5b0c4def-5a11-4798-8345-30c7a03e36c1", "intentionName": "要求退保"}, {"replyText": "需人工处理：存在ocr识别与名下保单不匹配", "stepDesc": "转人工", "stepCode": "T82", "intentionCode": "surrender_insurance", "nodeDesc": "引导登录：引导核身（未操作）-手机号核身（未提供）-查保单工具/截图（提供）-转人工，存在ocr识别与名下保单不匹配", "_score": 0.0074906712, "编号": "131a0550-0af6-4592-9aff-361e1e188f46", "intentionName": "要求退保"}, {"replyText": "需人工处理：用户执行完退保操作", "stepDesc": "转人工", "stepCode": "T49", "intentionCode": "surrender_insurance", "nodeDesc": "退保-目标退保多单：引导用户确认退保金额（不认可）-解释有损退（认可）-发送退保工具（操作）-转人工，用户操作退保后再次进线", "_score": 0.0074906712, "编号": "9f61dc35-8df3-46e1-aaad-8d9549e1549e", "intentionName": "要求退保"}, {"replyText": "需人工处理：引导自行操作后，用户未操作，再次进线", "stepDesc": "转人工", "stepCode": "T44", "intentionCode": "surrender_insurance", "nodeDesc": "退保-目标退保一单：引导用户确认退保金额（不认可）-解释有损退（认可）-发送退保工具（未操作）-引导自行操作（未操作）-转人工，引导自行操作后用户未操作", "_score": 0.0074906712, "编号": "e03b4979-9f9e-41de-a74b-0295e092a372", "intentionName": "要求退保"}, {"replyText": "需人工处理：引导自行操作后，用户操作完退保之后再次进线", "stepDesc": "转人工", "stepCode": "T53", "intentionCode": "surrender_insurance", "nodeDesc": "退保-目标退保多单：引导用户确认退保金额（认可）-发送退保工具（未操作）-引导自行操作（操作）-转人工，用户操作退保后再次进线", "_score": 0.0074906712, "编号": "b9ee8021-ea0a-4cab-8456-2683110aa53e", "intentionName": "要求退保"}, {"replyText": "需人工处理：一轮挽单后用户同意部分保留", "stepDesc": "转人工", "stepCode": "T74", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-目标挽单列表多张保单：无损第一轮（部分同意）-转人工，复杂场景：无损一挽，用户同意保留部分保单，需人工区分", "_score": 0.0074906712, "编号": "3538f56e-5ca8-4b52-ab62-6d7a4be43a10", "intentionName": "要求退保"}, {"replyText": "需人工处理：引导自行操作后，用户操作完退保之后再次进线", "stepDesc": "转人工", "stepCode": "T46", "intentionCode": "surrender_insurance", "nodeDesc": "退保-目标退保一单：引导用户确认退保金额（不认可）-解释有损退（认可）-发送退保工具（未操作）-引导自行操作（操作）-转人工，用户操作退保后再次进线", "_score": 0.0074906712, "编号": "b2065cd1-89ef-4809-9400-4249f008b8a8", "intentionName": "要求退保"}, {"replyText": "需人工处理：不确认退保转人工--多张挽单", "stepDesc": "转人工", "stepCode": "T73", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-目标挽单列表多张保单：无损第一轮（全部不同意）-无损第二轮（全部不同意）-退保损失讲解（全部不同意）-权益挽单（全部不同意）-解停挽单（全部不同意）-挽单失败确认保单（不认可）-转人工，用户否认申请退保保单信息", "_score": 0.0074906712, "编号": "56f65aa1-1427-4771-9652-60051a01429b", "intentionName": "要求退保"}, {"replyText": "需人工处理：发送退保连接后-用户再次进线，未操作退保", "stepDesc": "单张保单-发送退保连接后-用户再次进线，未操作退保", "stepCode": "T57", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-目标挽单列表仅一张保单：险类：${险类}-无损第一轮（未同意）-无损第二轮（未同意）-退保损失讲解（未同意）-权益挽单（未同意）-解停挽单（未同意）-挽单失败确认保单（未否认）-发送退保链接-用户再次进线：未操作退保，转人工", "_score": 0.0074906712, "编号": "e7905f33-fe07-43a9-8eda-624b96faded4", "intentionName": "要求退保"}, {"replyText": "需人工处理：提供了保单截图_存在未匹配的保单", "stepDesc": "转人工", "stepCode": "T27", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-定位保单-T-1有历史记录：T-1内有扣费截图-转人工，存在ocr识别与名下保单不匹配", "_score": 0.0074906712, "编号": "32e9241f-0c13-489d-b9e2-c894dee24c82", "intentionName": "要求退保"}, {"replyText": "进入挽单流程-定位保单-T-1有历史记录：T-1内有扣费截图-转人工，存在ocr识别失败", "stepDesc": "进入挽单流程-定位保单-T-1有历史记录：T-1内有扣费截图-转人工，存在ocr识别失败", "stepCode": "T01", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-定位保单-T-1有历史记录：T-1内有扣费截图-转人工，存在ocr识别失败", "_score": 0.0074906712, "编号": "1204b319-d726-4e6b-b375-17f5baee84a3", "intentionName": "要求退保"}, {"replyText": "需人工处理：扣费截图与手机号查询保单未匹配", "stepDesc": "转人工", "stepCode": "T20", "intentionCode": "surrender_insurance", "nodeDesc": "仅有赠险：赠险无需费用告知&索要扣费截图（全部我司扣费截图）-其他手机号确认（提供）-扣费截图与手机号查询保单未匹配，转人工", "_score": 0.0074906712, "编号": "9638c618-0d97-40d2-ab0c-cc80d33af9aa", "intentionName": "要求退保"}, {"replyText": "需人工处理：存在非我司扣费截图", "stepDesc": "进入挽单流程-定位保单-无历史记录：索要扣费截图（提供）-转人工，存在非我司扣费截图", "stepCode": "T21", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-定位保单-无历史记录：索要扣费截图（提供）-转人工，存在非我司扣费截图", "_score": 0.0074906712, "编号": "0b01e827-e40d-4560-a152-0c92fa94d2b6", "intentionName": "要求退保"}, {"replyText": "需人工处理：存在ocr识别失败", "stepDesc": "进入挽单流程-定位保单-无历史记录：索要扣费截图（提供）-转人工，存在ocr识别失败", "stepCode": "T06", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-定位保单-无历史记录：索要扣费截图（提供）-转人工，存在ocr识别失败", "_score": 0.0074906712, "编号": "030505d7-679d-4ea6-a3d1-58153ca487bb", "intentionName": "要求退保"}, {"replyText": "需人工处理：存在未匹配到退保保单", "stepDesc": "进入挽单流程-定位保单-无历史记录：索要扣费截图（未提供：提及全退，侧边栏无符合挽单保单）-转人工，存在未匹配到退保保单", "stepCode": "T04", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-定位保单-无历史记录：索要扣费截图（未提供：提及全退，侧边栏无符合挽单保单）-转人工，存在未匹配到退保保单", "_score": 0.0074906712, "编号": "a4e513c5-0cba-496f-9d21-b6602a2f04a3", "intentionName": "要求退保"}, {"replyText": "需人工处理：存在ocr识别失败", "stepDesc": "进入挽单流程-定位保单-无历史记录：索要扣费截图（未提供：未提及全退）-确认保单截图（提供）-转人工，存在ocr识别失败", "stepCode": "T62", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-定位保单-无历史记录：索要扣费截图（未提供：未提及全退）-确认保单截图（提供）-转人工，存在ocr识别失败", "_score": 0.0074906712, "编号": "72b3bbd3-6fc1-4e7a-9b62-7a19b2526152", "intentionName": "要求退保"}, {"replyText": "需人工处理：存在ocr识别与名下保单不匹配", "stepDesc": "进入挽单流程-定位保单-无历史记录：索要扣费截图（提供）-转人工，存在ocr识别与名下保单不匹配", "stepCode": "T07", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-定位保单-无历史记录：索要扣费截图（提供）-转人工，存在ocr识别与名下保单不匹配", "_score": 0.0074906712, "编号": "e6fc2db9-cd41-48ad-9331-4630779a82d2", "intentionName": "要求退保"}, {"replyText": "需人工处理：存在未匹配到退保保单", "stepDesc": "进入挽单流程-定位保单-无历史记录：索要扣费截图（未提供：未提及全退）-确认保单截图（未提供：提及全退，侧边栏无符合挽单保单）-转人工，存在未匹配到退保保单", "stepCode": "T05", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-定位保单-无历史记录：索要扣费截图（未提供：未提及全退）-确认保单截图（未提供：提及全退，侧边栏无符合挽单保单）-转人工，存在未匹配到退保保单", "_score": 0.0074906712, "编号": "698a032b-7038-46c0-91eb-ea8e12c79b94", "intentionName": "要求退保"}, {"replyText": "请点击上方发送给您的“极速退保”链接。操作的时候，一定要准确选好要退保的保单，认真确认退费金额，千万别误操作！要是有任何疑问，随时联系我们就行。", "stepDesc": "发送极速退保链接+操作流程(认可有损退)", "stepCode": "S01", "intentionCode": "surrender_insurance", "nodeDesc": "退保-目标退保一单：引导用户确认退保金额（不认可）-解释有损退（认可）-发送退保工具", "_score": 0.0074906712, "编号": "3ade9d94-38ac-4a3d-8fee-ef293fdf1012", "extraInfo": "[{\"miniChannel\":\"JJ_MINIAPP_ZAB\",\"miniContent\":\"极速退保通道\",\"miniImageUrl\":\"https://nmpcdn.zhonganib.com/nmp/1729840628541/2553bf1292a111efb7406ae51353cbbb.png?&w=700&h=592\",\"miniPath\":\"/pages/finance/commonTransfer/index?pageType=dischargeList&operateCode=${surrenderCode}\",\"miniTitle\":\"极速退保通道\",\"type\":\"miniprogrampage\"},{\"imageUrl\":\"https://nmpcdn.zhonganib.com/nmp/1747652416863/727c0f0c34a011f0a5c6b60542ca4545.png?&w=750&h=2815\",\"type\":\"image\"}]", "intentionName": "要求退保"}, {"replyText": "需人工处理：用户否认申请退保的保单信息", "stepDesc": "进入挽单流程-目标挽单列表仅一张保单：险类：无损第一轮（未同意）-无损第二轮（未同意）-退保损失讲解（未同意）-权益挽单（未同意）-解停挽单（未同意）-挽单失败确认保单（不认可）-转人工，用户否认申请退保的保单信息", "stepCode": "T09", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-目标挽单列表仅一张保单：险类：${险类}-无损第一轮（未同意）-无损第二轮（未同意）-退保损失讲解（未同意）-权益挽单（未同意）-解停挽单（未同意）-挽单失败确认保单（不认可）-转人工，用户否认申请退保的保单信息", "_score": 0.0074906712, "编号": "fd2c419f-c134-480f-97c6-f2fc5a17fe6d", "intentionName": "要求退保"}, {"replyText": "亲😃，麻烦您提供一下需要处理的保单扣费截图🤗，我马上帮您核实～", "stepDesc": "向用户要扣费截图", "stepCode": "C04", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-定位保单-无历史记录：索要扣费截图", "_score": 0.0074906712, "编号": "d819cd31-1779-4d4b-95a9-02f363dea8f6", "extraInfo": "[{\"imageUrl\":\"https://nmpcdn.zhonganib.com/nmp/1747652691552/163646e934a111f09b79fa4478b5c044.png?&w=750&h=4461\",\"type\":\"image\"}]", "intentionName": "要求退保"}, {"replyText": "请点击上方发送给您的“极速退保”链接。操作的时候，一定要准确选好要退保的保单，认真确认退费金额，千万别误操作！要是有任何疑问，随时联系我们就行。", "stepDesc": "发送极速退保链接+操作流程-多单(认可退费)", "stepCode": "S04", "intentionCode": "surrender_insurance", "nodeDesc": "退保-目标退保多单：引导用户确认退保金额（认可）-发送退保工具", "_score": 0.0074906712, "编号": "272d85c0-8cca-41d7-9904-4b5cefa4eeea", "extraInfo": "[{\"miniChannel\":\"JJ_MINIAPP_ZAB\",\"miniContent\":\"极速退保通道\",\"miniImageUrl\":\"https://nmpcdn.zhonganib.com/nmp/1729840628541/2553bf1292a111efb7406ae51353cbbb.png?&w=700&h=592\",\"miniPath\":\"/pages/finance/commonTransfer/index?pageType=dischargeList&operateCode=${surrenderCode}\",\"miniTitle\":\"极速退保通道\",\"type\":\"miniprogrampage\"},{\"imageUrl\":\"https://nmpcdn.zhonganib.com/nmp/1747652416863/727c0f0c34a011f0a5c6b60542ca4545.png?&w=750&h=2815\",\"type\":\"image\"}]", "intentionName": "要求退保"}, {"replyText": "非常能理解您，其实买保险就是花小钱保大钱，现在可能是花${次月扣费金额!}块钱，但是有风险发生的时候，这个钱就可以放大成几万，几十万来用，因为现在医院的医疗费用也是非常贵的，您现在就是做一个未雨绸缪，把风险转嫁出去。", "stepDesc": "多张--产品讲解（责任不清楚）", "stepCode": "Z19", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-目标挽单列表多张保单：无损第一轮", "_score": 0.0074906712, "编号": "5e9f6dfc-8288-462b-9e53-0c530e77da30", "intentionName": "要求退保"}, {"replyText": "进入挽单流程-目标挽单列表仅一张保单:流程结束，需人工处理：3轮挽单后同意保留保单", "stepDesc": "转人工", "stepCode": "T70", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-目标挽单列表仅一张保单:流程结束，3轮挽单后同意保留保单", "_score": 0.0074906712, "编号": "fe6325be-c6be-4455-b1fd-0c32c99394b3", "intentionName": "要求退保"}, {"replyText": "进入挽单流程-目标挽单列表多张保单:流程结束，需人工处理：4轮挽单后同意保留全部保单", "stepDesc": "转人工", "stepCode": "T66", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-目标挽单列表多张保单:流程结束，4轮挽单后同意保留全部保单", "_score": 0.0074906712, "编号": "5538a3c0-51d5-4280-a12d-36872cf649cc", "intentionName": "要求退保"}, {"replyText": "进入挽单流程-目标挽单列表仅一张保单:流程结束，需人工处理：5轮挽单后同意保留保单", "stepDesc": "转人工", "stepCode": "T72", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-目标挽单列表仅一张保单:流程结束，5轮挽单后同意保留保单", "_score": 0.0074906712, "编号": "12227ac3-c654-4c8a-9420-c6840ed1d527", "intentionName": "要求退保"}, {"replyText": "进入挽单流程-目标挽单列表多张保单:流程结束，需人工处理：1轮挽单后同意保留全部保单", "stepDesc": "转人工", "stepCode": "T63", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-目标挽单列表多张保单:流程结束，1轮挽单后同意保留全部保单", "_score": 0.0074906712, "编号": "3e12a91c-94a3-473e-aa41-9b2041aac6d9", "intentionName": "要求退保"}, {"replyText": "进入挽单流程-目标挽单列表多张保单:流程结束，需人工处理：2轮挽单后同意保留全部保单", "stepDesc": "转人工", "stepCode": "T64", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-目标挽单列表多张保单:流程结束，2轮挽单后同意保留全部保单", "_score": 0.0074906712, "编号": "1b4b03da-e48f-41f7-947a-31774fc642d8", "intentionName": "要求退保"}, {"replyText": "进入挽单流程-目标挽单列表多张保单:流程结束，需人工处理：5轮挽单后同意保留全部保单", "stepDesc": "转人工", "stepCode": "T67", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-目标挽单列表多张保单:流程结束，5轮挽单后同意保留全部保单", "_score": 0.0074906712, "编号": "e2603bc1-64ed-4cae-9dcd-1f44dcbab7a8", "intentionName": "要求退保"}, {"replyText": "需人工处理：用户否认申请退保保单信息", "stepDesc": "确认申请退保保单之后，用户犹豫未决", "stepCode": "T79", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-目标挽单列表多张保单：无损第一轮（全部不同意）-无损第二轮（全部不同意）-退保损失讲解（全部不同意）-权益挽单（全部不同意）-解停挽单（全部不同意）-挽单失败确认保单（不认可）-转人工，用户否认申请退保保单信息", "_score": 0.0074906712, "编号": "354a2986-b4ba-4c1e-801c-1ed63c1bab12", "intentionName": "要求退保"}, {"replyText": "退保的话您就没有现在这份保险保障啦。要是之后想重新投保，保险公司会按您当时实际年龄算保费，年龄越大，保费往往越高呢。而且要是重新投健康险，还得按重新投保时间重新算等待期。您再慎重考虑考虑呀🤗。", "stepDesc": "退保损失挽单--单张--医疗意外重疾", "stepCode": "Z21", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-目标挽单列表仅一张保单：险类：${险类}-无损第一轮（未同意）-无损第二轮（未同意）-退保损失讲解", "_score": 0.0074906712, "编号": "05a600c8-2fcb-425b-b5d9-64edf3bd369a", "intentionName": "要求退保"}, {"replyText": "退保的话您就没有现在这份保险保障啦。您可以考虑一下，您现在每个月缴费金额对比于您保障的房屋以及房屋内的附属物其实是很少的，而且要是您现在退保了，后期如果想要重新投的话咱们这个生效时间还是要重新计算的，还是希望能再慎重考虑一下🤗。", "stepDesc": "退保损失--单张--家财", "stepCode": "Z30", "intentionCode": "surrender_insurance", "nodeDesc": "进入挽单流程-目标挽单列表仅一张保单：险类：${险类}-无损第一轮（未同意）-无损第二轮（未同意）-退保损失讲解", "_score": 0.0074906712, "编号": "2a58f428-bafb-439f-9d4d-414bd8870931", "intentionName": "要求退保"}]