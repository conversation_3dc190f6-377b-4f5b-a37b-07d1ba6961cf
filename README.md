# Excel数据处理工具

一个功能强大的Excel数据处理工具，支持多种数据处理方式和AI智能功能。

## 🚀 快速开始

### 方式一：使用打包版本（推荐给普通用户）
1. 运行 `python build.py` 生成exe文件
2. 双击生成的exe文件即可使用
3. 无需安装Python环境

### 方式二：使用源码版本（推荐给技术用户）

#### Windows用户
1. 双击运行 `install.bat` 安装依赖
2. 双击运行 `start.bat` 启动应用

#### macOS/Linux用户
1. 运行 `chmod +x install.sh && ./install.sh` 安装依赖
2. 运行 `chmod +x start.sh && ./start.sh` 启动应用

## 📋 功能特点

- ✅ **原样输出**：直接复制列数据
- ✅ **执行算式**：数学运算处理
- ✅ **按字段取值**：JSON数据提取，生成多列
- ✅ **AI写公式**：智能生成Excel公式
- ✅ **AI自定义任务**：文本智能处理
- ✅ **试运行功能**：验证配置正确性
- ✅ **进度显示**：实时处理状态
- ✅ **一键打开文件**：处理完成后直接打开结果

## 🎯 使用场景

- 数据清洗和转换
- JSON数据字段提取
- 批量数学计算
- AI辅助数据分析
- Excel公式自动生成

## 📁 文件说明

- `excel_pyqt5_app.py` - 主程序
- `requirements.txt` - Python依赖列表
- `install.bat/install.sh` - 自动安装脚本
- `start.bat/start.sh` - 启动脚本
- `build.py` - 打包脚本
- `演示数据.xlsx` - 测试数据
- `使用说明.md` - 详细使用指南

## ⚠️ 系统要求

- Python 3.7+ （源码版本需要）
- Windows 7/8/10/11 或 macOS 10.14+ 或 Linux
- 网络连接（AI功能需要）

## 🔧 打包分发

### 生成exe文件
```bash
python build.py
```

### 手动打包
```bash
pip install pyinstaller
pyinstaller --onefile --windowed --name="Excel数据处理工具" excel_pyqt5_app.py
```

## 📞 技术支持

如有问题请查看：
1. `使用说明.md` - 基本使用方法
2. `PyQt5版本使用说明.md` - 详细功能说明
3. `问题排查指南.md` - 常见问题解决

## 📄 许可证

本项目仅供学习和内部使用。
