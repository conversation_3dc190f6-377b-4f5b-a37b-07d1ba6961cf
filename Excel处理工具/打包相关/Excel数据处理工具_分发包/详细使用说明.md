# Excel数据处理工具 - PyQt5版本使用说明

## 🎉 成功启动！

您的Excel数据处理工具PyQt5版本已经成功启动！应用程序现在应该显示在您的屏幕上。

## 📋 应用界面说明

### 主要区域

1. **📁 文件选择区域**
   - "选择Excel文件"按钮：选择要处理的Excel文件
   - "读取Excel"按钮：读取并解析选中的文件
   - 文件路径显示：显示当前选中的文件名

2. **💾 结果存储区域**
   - "新建工作簿"：将处理结果保存到新的Excel文件
   - "表格后方空白的列"：在原文件中添加结果列

3. **🔧 任务配置区域**
   - 显示Excel文件中的所有列
   - 每列可以独立配置处理方式
   - 支持滚动查看所有列

4. **控制按钮区域**
   - "试运行"：处理第一行数据，验证配置
   - "开始执行"：批量处理所有数据
   - "帮助"：显示详细使用说明

5. **📊 状态信息区域**
   - 进度条：显示处理进度
   - 状态标签：显示当前操作状态

## 🚀 使用步骤

### 第一步：选择和读取文件
1. 点击"📂 选择Excel文件"按钮
2. 在文件对话框中选择您的Excel文件（.xlsx或.xls）
3. 点击"📖 读取Excel"按钮加载文件数据
4. 系统会显示文件的行数和列数信息

### 第二步：配置处理任务
对于每一列，您可以：
1. **勾选"处理"复选框**：表示要处理这一列
2. **选择处理方式**：从下拉菜单中选择
3. **填写任务详情**：根据处理方式填写具体配置

### 第三步：选择结果存储方式
- **新建工作簿**（推荐）：创建新文件，原文件不变
- **表格后方空白的列**：在原文件中添加结果列

### 第四步：验证和执行
1. **试运行**：点击"🧪 试运行"按钮
   - 处理第一行数据
   - 在弹窗中查看结果
   - 确认配置是否正确
2. **开始执行**：点击"▶️ 开始执行"按钮
   - 批量处理所有数据
   - 显示处理进度
   - 完成后显示结果文件路径

## 🔧 处理方式详解

### 1. 执行算式
- **用途**：对数值列进行数学运算
- **配置示例**：
  - `(x+2)*3` - 给数值加2后乘以3
  - `x/100` - 将数值除以100
  - `x**2` - 计算数值的平方
- **说明**：使用 `x` 代表当前单元格的值

### 2. 按字段取值
- **用途**：从JSON数据中提取特定字段
- **配置示例**：
  - `name` - 提取name字段
  - `name|age|city` - 提取多个字段，用|分隔
- **说明**：单元格内容必须是JSON格式

### 3. AI写公式
- **用途**：让AI生成Excel公式
- **配置示例**：
  - `计算A列和B列的和`
  - `统计大于100的数量`
  - `计算平均值`
- **说明**：用自然语言描述需要的公式功能

### 4. AI自定义任务
- **用途**：让AI处理复杂的文本任务
- **配置示例**：
  - `提取文本中的关键信息`
  - `分析用户情感倾向`
  - `总结文本内容`
- **说明**：会逐行调用AI处理，耗时较长

## ⚠️ 注意事项

1. **文件格式**：支持.xlsx和.xls格式的Excel文件
2. **数据备份**：建议选择"新建工作簿"以保护原始数据
3. **AI功能**：需要网络连接，处理大量数据时请耐心等待
4. **错误处理**：如果某行数据处理失败，结果会显示为空值
5. **进度监控**：处理过程中可以通过进度条查看进度

## 🎯 使用技巧

1. **先试运行**：在批量处理前，务必使用试运行功能验证配置
2. **分批处理**：对于大量数据，可以分批处理以提高效率
3. **配置保存**：处理完成后，配置会保留，方便处理类似文件
4. **错误排查**：如果处理失败，检查数据格式和配置是否正确

## 📊 示例场景

### 场景1：数值计算
- **列**：销售额
- **处理方式**：执行算式
- **配置**：`x * 1.1`（增加10%）

### 场景2：JSON数据提取
- **列**：用户信息
- **处理方式**：按字段取值
- **配置**：`name|age|email`

### 场景3：AI公式生成
- **列**：数据列
- **处理方式**：AI写公式
- **配置**：`计算当前行与上一行的差值`

### 场景4：文本分析
- **列**：用户反馈
- **处理方式**：AI自定义任务
- **配置**：`分析反馈情感并分类为正面、负面或中性`

## 🆘 故障排除

如果遇到问题：
1. 检查文件是否为有效的Excel格式
2. 确认网络连接（AI功能需要）
3. 验证配置语法是否正确
4. 查看状态栏的错误信息
5. 重启应用程序

## 📞 技术支持

- 应用版本：PyQt5 v2.0
- Python版本要求：3.7+
- 依赖库：PyQt5, pandas, requests, openpyxl

现在您可以开始使用这个功能强大的Excel数据处理工具了！
