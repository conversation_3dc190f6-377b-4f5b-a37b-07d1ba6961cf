# Excel数据处理工具 - 使用指南

## 🚀 快速开始
1. 双击"Excel数据处理工具"启动应用
2. 参考"使用说明.md"了解基本功能
3. 查看"详细使用说明.md"了解高级功能
4. 可以使用"演示数据.xlsx"进行测试

## 📋 功能特点
✅ 原样输出：直接复制列数据
✅ 执行算式：数学运算处理 (如: x*2, (x+1)/3)
✅ 按字段取值：JSON数据提取，自动生成多列
✅ AI写公式：智能生成Excel公式
✅ AI自定义任务：文本智能处理
✅ 试运行功能：验证配置正确性
✅ 进度显示：实时处理状态
✅ 一键打开文件：处理完成后直接打开结果

## 🎯 使用流程
1. 启动应用
2. 选择Excel文件并读取
3. 配置需要处理的列
4. 选择处理方式和填写配置
5. 试运行验证配置
6. 开始执行批量处理
7. 查看和打开结果文件

## ⚠️ 系统要求
- macOS 10.14+ 或 Windows 7/8/10/11 或 Linux
- 网络连接（AI功能需要）
- 无需安装Python环境

## 🔧 处理方式说明

### 原样输出
- 用途：直接复制列数据
- 配置：原样输出该列

### 执行算式
- 用途：数学运算
- 配置示例：x*2, (x+100)/2, x**2
- 说明：x代表单元格的值

### 按字段取值
- 用途：从JSON中提取字段，生成多列
- 配置示例：name|age|city
- 说明：会生成3个新列，分别是name、age、city的值

### AI写公式
- 用途：生成Excel公式
- 配置示例：计算A列和B列的和
- 说明：AI会生成对应的Excel公式

### AI自定义任务
- 用途：智能文本处理
- 配置示例：分析文本情感，提取关键信息
- 说明：会逐行调用AI处理，耗时较长

## 📞 技术支持
如有问题请查看详细使用说明文档，或检查网络连接。

版本：v2.0
更新时间：2025-06-16
